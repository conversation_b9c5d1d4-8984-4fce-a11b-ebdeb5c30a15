// ===== FILE UPLOAD FUNCTIONALITY =====

let selectedFiles = [];
let uploadInitialized = false; // Flag to prevent multiple initialization

/**
 * Initialize upload functionality
 */
function initializeUpload() {
    // Prevent multiple initialization
    if (uploadInitialized) {
        console.log('⚠️ Upload already initialized, skipping...');
        return;
    }
    
    const dropArea = document.getElementById('dropArea');
    const fileInput = document.getElementById('fileInput');

    if (!dropArea || !fileInput) {
        console.warn('Upload elements not found');
        return;
    }

    console.log('🔧 Initializing upload functionality...');
    
    // Drag and drop event listeners
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    dropArea.addEventListener('drop', handleDrop, false);
    dropArea.addEventListener('click', () => {
        fileInput.click();
    });
    fileInput.addEventListener('change', handleFileSelect);

    // Upload form handler
    const uploadForm = document.getElementById('uploadForm');
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleUploadSubmit);
    }
    
    uploadInitialized = true;
    console.log('✅ Upload functionality initialized');
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight() {
    document.getElementById('dropArea')?.classList.add('dragover');
}

function unhighlight() {
    document.getElementById('dropArea')?.classList.remove('dragover');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    if (files.length > 0) {
        handleMultipleFileSelection(files);
    }
}

function handleFileSelect(e) {
    if (e.target.files.length > 0) {
        handleMultipleFileSelection(e.target.files);
    }
}

function handleMultipleFileSelection(files) {
    const validFiles = [];
    const errors = [];
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        if (file.type !== 'application/pdf') {
            errors.push(`${file.name}: Sadece PDF dosyaları kabul edilir!`);
            continue;
        }
        
        if (file.size > 100 * 1024 * 1024) { // 100MB
            errors.push(`${file.name}: Dosya boyutu 100MB'dan büyük olamaz!`);
            continue;
        }
        
        validFiles.push(file);
    }
    
    if (errors.length > 0) {
        showToast(errors.join('\n'), 'error', 6000);
    }
    
    if (validFiles.length > 0) {
        selectedFiles = validFiles;
        showSelectedFiles(validFiles);
    }
}

function showSelectedFiles(files) {
    const selectedDiv = document.getElementById('selectedFilesDiv');
    const filesList = document.getElementById('selectedFilesList');
    const totalSize = document.getElementById('selectedFilesTotalSize');

    if (!selectedDiv || !filesList || !totalSize) return;

    // Clear previous list
    filesList.innerHTML = '';
    
    // Add each file to the list
    let totalBytes = 0;
    files.forEach((file, index) => {
        totalBytes += file.size;
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item flex items-center py-2 px-3 text-sm';
        fileItem.innerHTML = `
            <span class="file-name text-blue-800" title="${file.name}">${file.name}</span>
            <span class="file-size">${formatFileSize(file.size)}</span>
        `;
        filesList.appendChild(fileItem);
    });
    
    totalSize.textContent = `Toplam: ${files.length} dosya - ${formatFileSize(totalBytes)}`;
    
    selectedDiv.classList.remove('hidden');
    const dropArea = document.getElementById('dropArea');
    if (dropArea) {
        dropArea.style.display = 'none';
    }
    
    // Update book name field based on file count
    updateBookNameField(files.length);
}

function updateBookNameField(fileCount) {
    const bookNameInput = document.getElementById('bookName');
    if (!bookNameInput) return;
    
    if (fileCount > 1) {
        // Multiple files: disable and show info
        bookNameInput.disabled = true;
        bookNameInput.placeholder = 'Çoklu dosya - her dosya kendi adını kullanır';
        bookNameInput.value = '';
        bookNameInput.classList.add('bg-gray-100', 'text-gray-500');
    } else {
        // Single file: enable
        bookNameInput.disabled = false;
        bookNameInput.placeholder = 'Tek dosya için: Örn: Osmanlı Tarihi';
        bookNameInput.classList.remove('bg-gray-100', 'text-gray-500');
    }
}

function clearSelectedFiles() {
    selectedFiles = [];
    const selectedDiv = document.getElementById('selectedFilesDiv');
    const dropArea = document.getElementById('dropArea');
    const fileInput = document.getElementById('fileInput');

    if (selectedDiv) selectedDiv.classList.add('hidden');
    if (dropArea) dropArea.style.display = 'block';
    if (fileInput) fileInput.value = '';
    
    // Reset book name field
    updateBookNameField(0);
    
}

async function handleUploadSubmit(e) {
    e.preventDefault();

    const topic = document.getElementById('topic')?.value.trim();
    const bookName = document.getElementById('bookName')?.value.trim();

    if (!selectedFiles || selectedFiles.length === 0) {
        showToast('Lütfen en az bir PDF dosyası seçin!', 'warning', 3000);
        return;
    }

    if (!topic) {
        showToast('Lütfen bir konu adı yazın!', 'warning', 3000);
        return;
    }

    // For multiple files, book name is optional - we'll use file names
    if (!bookName && selectedFiles.length === 1) {
        showToast('Tek dosya yükleme için kitap adı gerekli!', 'warning', 3000);
        return;
    }

    try {
        showLoading(`${selectedFiles.length} PDF dosyası yükleniyor ve işleniyor...`);
        
        let totalChunks = 0;
        let successCount = 0;
        
        // Upload each file sequentially
        for (let i = 0; i < selectedFiles.length; i++) {
            const file = selectedFiles[i];
            console.log(`📤 Uploading file ${i+1}/${selectedFiles.length}: ${file.name}`);
            
            // Generate smart book name
            let smartBookName;
            if (selectedFiles.length === 1) {
                // Single file: use provided name or file name
                smartBookName = bookName || file.name.replace('.pdf', '');
            } else {
                // Multiple files: use file name as book name
                smartBookName = file.name.replace('.pdf', '');
            }
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('topic', topic);
            formData.append('book_name', smartBookName);
            
            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    totalChunks += result.chunks_added;
                    successCount++;
                } else {
                    console.error(`❌ Error uploading ${file.name}:`, result.error);
                    showToast(`${file.name} yükleme hatası: ${result.error}`, 'error', 4000);
                }
            } catch (error) {
                console.error(`❌ Error uploading ${file.name}:`, error);
                showToast(`${file.name} yükleme hatası: ${error.message}`, 'error', 4000);
            }
        }

        if (successCount > 0) {
            showToast(`${successCount}/${selectedFiles.length} PDF başarıyla eklendi! Toplam ${totalChunks} parça oluşturuldu.`, 'success', 8000);
            
            // Sadece formu temizle, drop area'yı tekrar gösterme
            selectedFiles = [];
            const selectedDiv = document.getElementById('selectedFilesDiv');
            if (selectedDiv) selectedDiv.classList.add('hidden');
            
            // Form alanlarını temizle
            document.getElementById('uploadForm')?.reset();
            updateBookNameField(0);
            
            // UI güncellemesi
            console.log('📁 PDFs yüklendi, UI güncelleniyor...');
            setTimeout(async () => {
                try {
                    console.log('🔄 Stats yenileniyor...');
                    await refreshStats();
                    
                    // DOM elementlerinin varlığını kontrol et
                    if (document.getElementById('booksContainer')) {
                        console.log('📚 Books force refresh...');
                        if (typeof loadBooks === 'function') {
                            await loadBooks(true);
                        }
                    }
                    
                    console.log('🏷️ Topics yenileniyor...');
                    if (typeof loadTopics === 'function') {
                        await loadTopics();
                    }
                    
                    console.log('✅ Multi-PDF upload UI güncellendi!');
                } catch (error) {
                    console.error('❌ Multi-PDF upload UI güncelleme hatası:', error);
                }
            }, 500);
        } else {
            showToast('Hiçbir dosya yüklenemedi!', 'error', 6000);
        }
        
    } catch (error) {
        showToast('Yükleme hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

// Make function global for HTML onclick
window.clearSelectedFiles = clearSelectedFiles;

// Auto-initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeUpload);
} else {
    initializeUpload();
}
