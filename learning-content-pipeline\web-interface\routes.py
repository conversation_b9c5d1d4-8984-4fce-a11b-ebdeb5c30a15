"""
Flask routes for RAG Learning System
"""
import json
import logging
import shutil
from datetime import datetime

from flask import request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

from config import UPLOAD_FOLDER, BOOKS_FOLDER, OUTPUTS_FOLDER, rate_limiter
import config
from utils import (allowed_file, sanitize_filename, create_dynamic_manifest, 
                   sanitize_topic_name, sanitize_book_name, validate_file_upload, 
                   is_safe_path_component, TempFileManager, cleanup_orphaned_files,
                   get_books)

logger = logging.getLogger(__name__)

def register_routes(app):
    """Register all routes with the Flask app"""
    
    @app.route('/')
    def index():
        """Home page"""
        return render_template('index.html')

    @app.route('/api/status')
    def api_status():
        """System status API"""
        if config.rag_system is None:
            return jsonify({"error": "RAG system not initialized"}), 500
        
        # Cache bypass control (with query param)
        force_refresh = request.args.get('_') is not None
        if force_refresh:
            logger.info("🔄 Force refresh requested")
        
        stats = config.rag_system.get_system_stats()
        logger.info(f"📊 Status API response: {len(stats.get('books', []))} topics, force={force_refresh}")
        return jsonify(stats)

    @app.route('/api/upload', methods=['POST'])
    def api_upload():
        """PDF upload API with enhanced input validation and rate limiting"""
        if config.rag_system is None:
            return jsonify({"error": "RAG system not ready"}), 500
        
        # Rate limiting check
        client_ip = request.remote_addr or 'unknown'
        allowed, reason = rate_limiter.is_allowed(client_ip, 'file_upload')
        if not allowed:
            logger.warning(f"Rate limit exceeded for upload from IP {client_ip}: {reason}")
            return jsonify({"error": reason}), 429
        
        if 'file' not in request.files:
            return jsonify({"error": "No file selected"}), 400
        
        file = request.files['file']
        topic = request.form.get('topic', '').strip()
        book_name = request.form.get('book_name', '').strip()
        
        # Enhanced validation for topic
        topic_valid, sanitized_topic, topic_error = sanitize_topic_name(topic)
        if not topic_valid:
            return jsonify({"error": f"Topic validation failed: {topic_error}"}), 400
        
        # Enhanced validation for book name (if provided)
        sanitized_book_name = book_name
        if book_name:  # Book name might be optional for multi-file uploads
            book_valid, sanitized_book_name, book_error = sanitize_book_name(book_name)
            if not book_valid:
                return jsonify({"error": f"Book name validation failed: {book_error}"}), 400
        
        # File validation
        if not file.filename:
            return jsonify({"error": "No file selected"}), 400
            
        file_data = {
            'filename': file.filename,
            'size': len(file.read())
        }
        file.seek(0)  # Reset file pointer after reading size
        
        file_valid, file_error = validate_file_upload(file_data)
        if not file_valid:
            return jsonify({"error": f"File validation failed: {file_error}"}), 400
        
        if file and allowed_file(file.filename):
            # Secure filename handling
            original_filename = file.filename
            filename = secure_filename(original_filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            
            # Additional security check for filename
            if not is_safe_path_component(filename):
                return jsonify({"error": "Filename contains unsafe characters"}), 400
            
            # Use TempFileManager for automatic cleanup
            with TempFileManager() as temp_manager:
                try:
                    # Create temporary file for upload
                    temp_file = temp_manager.create_temp_file(suffix='.pdf', directory=UPLOAD_FOLDER)
                    file.save(str(temp_file))
                    
                    # Make book and topic names Windows-safe for filesystem
                    safe_topic = sanitize_filename(sanitized_topic)
                    safe_book_name = sanitize_filename(sanitized_book_name) if sanitized_book_name else sanitize_filename(original_filename.replace('.pdf', ''))
                    
                    # Validate path components
                    if not is_safe_path_component(safe_topic) or not is_safe_path_component(safe_book_name):
                        return jsonify({"error": "Topic or book name contains unsafe path components"}), 400
                    
                    # Create book folder structure
                    topic_folder = BOOKS_FOLDER / safe_topic
                    book_folder = topic_folder / safe_book_name
                    book_folder.mkdir(parents=True, exist_ok=True)
                    
                    # Move file to final location
                    final_path = book_folder / original_filename
                    shutil.move(str(temp_file), str(final_path))
                    
                    # Remove from temp manager since we successfully moved it
                    temp_manager.temp_files.remove(temp_file)
                    
                    # Add to RAG system (with sanitized names)
                    result = config.rag_system.add_pdf_to_system(str(final_path), sanitized_topic, sanitized_book_name)
                    
                    if result["success"]:
                        logger.info(f"📁 PDF added: {sanitized_book_name} ({sanitized_topic})")
                        
                        return jsonify({
                            "success": True,
                            "message": f"PDF successfully added. {result['chunks_added']} chunks created.",
                            "chunks_added": result["chunks_added"]
                        })
                    else:
                        # Clean up file on processing failure
                        try:
                            if final_path.exists():
                                final_path.unlink()
                        except Exception as cleanup_error:
                            logger.warning(f"Failed to cleanup file after processing error: {cleanup_error}")
                        return jsonify({"error": result["error"]}), 500
                        
                except Exception as e:
                    logger.error(f"Upload processing error: {e}")
                    return jsonify({"error": f"Upload processing failed: {str(e)}"}), 500
                    # TempFileManager will automatically cleanup on exception
        
        return jsonify({"error": "Invalid file format"}), 400

    @app.route('/api/create-learning-path', methods=['POST'])
    def api_create_learning_path():
        """Learning path creation API with enhanced input validation and rate limiting"""
        if config.rag_system is None:
            return jsonify({"error": "RAG system not ready"}), 500
        
        # Rate limiting check
        client_ip = request.remote_addr or 'unknown'
        allowed, reason = rate_limiter.is_allowed(client_ip, 'path_creation')
        if not allowed:
            logger.warning(f"Rate limit exceeded for path creation from IP {client_ip}: {reason}")
            remaining = rate_limiter.get_remaining_requests(client_ip, 'path_creation')
            return jsonify({
                "error": reason,
                "remaining_requests": remaining
            }), 429
        
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "Request body must be valid JSON"}), 400
                
        except Exception as e:
            return jsonify({"error": f"Invalid JSON format: {str(e)}"}), 400
        
        topic = data.get('topic', '').strip()
        languages = data.get('languages', ['tr'])
        
        # Backward compatibility: single language support
        if 'language' in data and 'languages' not in data:
            languages = [data.get('language', 'tr')]
        
        # Enhanced topic validation
        topic_valid, sanitized_topic, topic_error = sanitize_topic_name(topic)
        if not topic_valid:
            return jsonify({"error": f"Topic validation failed: {topic_error}"}), 400
        
        # Language validation
        if not languages or not isinstance(languages, list) or len(languages) == 0:
            return jsonify({"error": "At least one language must be selected"}), 400
        
        # Validate each language code
        supported_languages = ['tr', 'en', 'de', 'it', 'ru', 'fr', 'es', 'pt']
        for lang in languages:
            if not isinstance(lang, str) or lang not in supported_languages:
                return jsonify({"error": f"Unsupported language: {lang}. Supported languages: {', '.join(supported_languages)}"}), 400
        
        # Limit number of languages to prevent abuse
        if len(languages) > 8:
            return jsonify({"error": "Maximum 8 languages allowed per request"}), 400
        
        logger.info(f"🌐 API call: Topic='{sanitized_topic}', Languages={languages}")
        
        try:
            result = config.rag_system.create_learning_path(sanitized_topic, languages)
            
            if result.get("success"):
                return jsonify(result)
            else:
                return jsonify({"error": result.get("error", "Unknown error")}), 500
                
        except Exception as e:
            logger.error(f"Learning path creation error: {e}")
            return jsonify({"error": f"Path creation failed: {str(e)}"}), 500

    @app.route('/api/delete-learning-path', methods=['POST'])
    def api_delete_learning_path():
        """Delete learning path"""
        try:
            data = request.get_json()
            path_id = data.get('path_id')  # path_id comes from frontend
            
            if not path_id:
                return jsonify({"error": "Path ID not specified"}), 400
            
            # Check and delete content folder
            content_folder = OUTPUTS_FOLDER / "content" / "tr" / path_id
            if not content_folder.exists():
                return jsonify({"error": "Path not found"}), 404
            
            path_title = path_id.replace('-', ' ').title()
            
            # Delete content folder
            shutil.rmtree(content_folder)
            logger.info(f"TR content folder deleted: {path_id}")
            
            # Delete English content if exists
            en_content_folder = OUTPUTS_FOLDER / "content" / "en" / path_id
            if en_content_folder.exists():
                shutil.rmtree(en_content_folder)
                logger.info(f"EN content folder deleted: {path_id}")
            
            # Check manifest file - delete if it's for this path
            manifest_file = OUTPUTS_FOLDER / "manifest.json"
            if manifest_file.exists():
                try:
                    with open(manifest_file, 'r', encoding='utf-8') as f:
                        manifest = json.load(f)
                    
                    # If manifest is for this path, delete it
                    if manifest.get('pathId') == path_id:
                        manifest_file.unlink()
                        logger.info(f"Manifest file deleted: {path_id}")
                except Exception as e:
                    logger.warning(f"Manifest file check error: {e}")
            
            logger.info(f"🗑️ Path completely deleted: {path_id}")
            
            return jsonify({
                "success": True,
                "message": f"'{path_title}' path deleted."
            })
            
        except Exception as e:
            logger.error(f"Path deletion error: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/delete-all-learning-paths', methods=['POST'])
    def api_delete_all_learning_paths():
        """Delete all learning paths (all languages) and clear manifest"""
        try:
            deleted_tr = 0
            deleted_en = 0

            # Delete TR paths
            tr_folder = OUTPUTS_FOLDER / "content" / "tr"
            if tr_folder.exists():
                for path_dir in tr_folder.iterdir():
                    if path_dir.is_dir():
                        shutil.rmtree(path_dir)
                        deleted_tr += 1

            # Delete EN paths if exist
            en_folder = OUTPUTS_FOLDER / "content" / "en"
            if en_folder.exists():
                for path_dir in en_folder.iterdir():
                    if path_dir.is_dir():
                        shutil.rmtree(path_dir)
                        deleted_en += 1

            # Remove manifest file if present
            manifest_file = OUTPUTS_FOLDER / "manifest.json"
            if manifest_file.exists():
                try:
                    manifest_file.unlink()
                except Exception as e:
                    logger.warning(f"Manifest delete warning: {e}")

            total_deleted = deleted_tr + deleted_en
            return jsonify({
                "success": True,
                "message": f"Toplam {total_deleted} patika silindi (TR: {deleted_tr}, EN: {deleted_en})."
            })
        except Exception as e:
            logger.error(f"Bulk learning paths deletion error: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/delete-book', methods=['POST'])
    def api_delete_book():
        """Delete book"""
        try:
            data = request.get_json()
            topic = data.get('topic')
            book_name = data.get('book_name')
            
            if not topic or not book_name:
                return jsonify({"error": "Topic ve kitap adı belirtilmelidir"}), 400
            
            # Check if book folder exists
            book_folder = BOOKS_FOLDER / topic / book_name
            if not book_folder.exists():
                return jsonify({"error": "Kitap bulunamadı"}), 404
            
            # Delete book folder
            shutil.rmtree(book_folder)
            logger.info(f"Book deleted: {topic}/{book_name}")
            
            # Check if topic folder is empty, if so delete it
            topic_folder = BOOKS_FOLDER / topic
            if topic_folder.exists() and not any(topic_folder.iterdir()):
                shutil.rmtree(topic_folder)
            logger.info(f"Empty topic folder deleted: {topic}")
        
            logger.info(f"🗑️ Book completely deleted: {topic}/{book_name}")
            return jsonify({"message": f"Kitap başarıyla silindi: {book_name}"}), 200
            
        except Exception as e:
            logger.error(f"Book deletion error: {e}")
            return jsonify({"error": f"Silme hatası: {str(e)}"}), 500

    @app.route('/api/delete-all-books', methods=['POST'])
    def api_delete_all_books():
        """Delete all books and topics"""
        try:
            if not BOOKS_FOLDER.exists():
                return jsonify({"success": True, "message": "Silinecek kitap yok."})

            deleted_topics = 0
            # Remove all topic folders under books
            for topic_dir in BOOKS_FOLDER.iterdir():
                if topic_dir.is_dir():
                    shutil.rmtree(topic_dir)
                    deleted_topics += 1

            return jsonify({
                "success": True,
                "message": f"Tüm kitaplar silindi. Toplam konu klasörü: {deleted_topics}."
            })
        except Exception as e:
            logger.error(f"Bulk books deletion error: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/topics')
    def api_topics():
        """List existing topics (for autocomplete)"""
        try:
            topics = set()
            
            # Collect topics from books folder
            if BOOKS_FOLDER.exists():
                for topic_folder in BOOKS_FOLDER.iterdir():
                    if topic_folder.is_dir():
                        # Convert folder name to original form (reverse of sanitize)
                        topics.add(topic_folder.name)
            
            # Also add path topics from outputs
            content_folder = OUTPUTS_FOLDER / "content" / "tr"
            if content_folder.exists():
                for path_folder in content_folder.iterdir():
                    if path_folder.is_dir():
                        # Convert dashes to spaces and title case
                        topic_name = path_folder.name.replace("-", " ").title()
                        topics.add(topic_name)
            
            return jsonify({"topics": sorted(list(topics))})
            
        except Exception as e:
            logger.error(f"Topic list retrieval error: {e}")
            return jsonify({"topics": []})

    @app.route('/api/books')
    def api_books():
        """Get books with pagination support"""
        try:
            # Get pagination parameters
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)  # Max 100 per page
            topic_filter = request.args.get('topic', None)
            
            # Validate parameters
            if page < 1:
                page = 1
            if per_page < 1:
                per_page = 20
            
            # Get paginated books
            result = get_books(page=page, per_page=per_page, topic_filter=topic_filter)
            
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"Books API error: {e}")
            return jsonify({"error": str(e)}), 500

    @app.route('/api/learning-paths')
    def api_learning_paths():
        """List existing learning paths"""
        try:
            paths = []
            content_folder = OUTPUTS_FOLDER / "content" / "tr"
            
            if content_folder.exists():
                for path_folder in content_folder.iterdir():
                    if path_folder.is_dir():
                        chapters = []
                        for chapter_file in sorted(path_folder.glob("chapter*.md")):
                            chapters.append({
                                "id": chapter_file.stem,
                                "title": chapter_file.stem.replace("chapter", "Chapter "),
                                "file": str(chapter_file.relative_to(OUTPUTS_FOLDER))
                            })
                        
                        if chapters:
                            paths.append({
                                "pathId": path_folder.name,
                                "title": path_folder.name.replace("-", " ").title(),
                                "chapters": chapters
                            })
            
            return jsonify({"paths": paths})
            
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route('/content/<path:filename>')
    def serve_content(filename):
        """Serve content files"""
        return send_from_directory(OUTPUTS_FOLDER / "content", filename)

    @app.route('/path/<path_id>')
    def view_learning_path(path_id):
        """View learning path in detail"""
        try:
            # Check if path folder exists
            path_folder = OUTPUTS_FOLDER / "content" / "tr" / path_id
            if not path_folder.exists():
                return "Path not found.", 404
            
            # Get path information from manifest file
            manifest_file = OUTPUTS_FOLDER / "manifest.json"
            manifest = None
            
            if manifest_file.exists():
                with open(manifest_file, 'r', encoding='utf-8') as f:
                    temp_manifest = json.load(f)
                    # If manifest is not for this path, create dynamically
                    if temp_manifest.get('pathId') != path_id:
                        manifest = create_dynamic_manifest(path_id, path_folder)
                    else:
                        manifest = temp_manifest
            else:
                # Create manifest dynamically if doesn't exist
                manifest = create_dynamic_manifest(path_id, path_folder)
            
            # Load chapter contents
            chapters_with_content = []
            for chapter in manifest.get('chapters', []):
                # Fix file path (support both \ and /)
                if isinstance(chapter.get('file'), str):
                    chapter_file = OUTPUTS_FOLDER / chapter['file'].replace('\\', '/')
                else:
                    chapter_file = path_folder / f"{chapter['id']}.md"
                    
                if chapter_file.exists():
                    with open(chapter_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Remove front matter
                        if content.startswith('---'):
                            content = content.split('---', 2)[-1].strip()
                        
                        chapters_with_content.append({
                            **chapter,
                            'content': content
                        })
            
            return render_template('learning_path.html', 
                                 path=manifest, 
                                 chapters=chapters_with_content)
            
        except Exception as e:
            logger.error(f"Path viewing error: {e}")
            return f"Error: {e}", 500

    @app.route('/api/cleanup-orphaned-files', methods=['POST'])
    def api_cleanup_orphaned_files():
        """Manual cleanup of orphaned files"""
        try:
            # Clean up orphaned files in upload folder
            cleanup_orphaned_files(UPLOAD_FOLDER, max_age_hours=1)
            
            # Clean up orphaned files in books folder
            cleanup_orphaned_files(BOOKS_FOLDER, max_age_hours=24)
            
            # Clean up orphaned files in outputs folder
            cleanup_orphaned_files(OUTPUTS_FOLDER, max_age_hours=24)
            
            # Clean up rate limiter entries
            rate_limiter.cleanup_old_entries()
            
            return jsonify({
                "success": True,
                "message": "Orphaned files cleanup completed"
            })
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
            return jsonify({"error": f"Cleanup failed: {str(e)}"}), 500

    @app.errorhandler(RequestEntityTooLarge)
    def handle_file_too_large(e):
        return jsonify({"error": "File too large. Maximum 100MB."}), 413
