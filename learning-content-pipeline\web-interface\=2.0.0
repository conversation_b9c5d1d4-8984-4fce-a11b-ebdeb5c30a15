Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: torch in c:\users\<USER>\appdata\roaming\python\python313\site-packages (2.6.0+cu126)
Collecting torch
  Using cached torch-2.8.0-cp313-cp313-win_amd64.whl.metadata (30 kB)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (4.13.2)
Collecting sympy>=1.13.3 (from torch)
  Using cached sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (3.4.2)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (2025.3.0)
Requirement already satisfied: setuptools in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from torch) (80.8.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from sympy>=1.13.3->torch) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from jinja2->torch) (3.0.2)
Using cached torch-2.8.0-cp313-cp313-win_amd64.whl (241.3 MB)
Using cached sympy-1.14.0-py3-none-any.whl (6.3 MB)
Installing collected packages: sympy, torch
  Attempting uninstall: sympy
    Found existing installation: sympy 1.13.1
    Uninstalling sympy-1.13.1:
      Successfully uninstalled sympy-1.13.1
  Attempting uninstall: torch
    Found existing installation: torch 2.6.0+cu126
    Uninstalling torch-2.6.0+cu126:
      Successfully uninstalled torch-2.6.0+cu126

