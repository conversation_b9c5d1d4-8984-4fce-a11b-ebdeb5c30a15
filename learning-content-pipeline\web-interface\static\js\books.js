// ===== BOOKS FUNCTIONALITY =====

/**
 * Load books directly
 * @param {boolean} forceRefresh - Force refresh (ignored, kept for compatibility)
 */
async function loadBooks(forceRefresh = false) {
    const container = document.getElementById('booksContainer');
    
    // DOM element kontrolü
    if (!container) {
        console.error('❌ booksContainer elementi bulunamadı!');
        return;
    }
    
    try {
        // Force refresh için cache bypass
        const url = forceRefresh ? '/api/status?_=' + Date.now() : '/api/status';
        console.log('📡 API çağrısı:', url, forceRefresh ? '(FORCE)' : '(normal)');
        
        const stats = await apiCall(url);
        console.log('📊 Books API yanıtı:', {
            totalBooks: stats.books?.length || 0,
            forceRefresh,
            booksData: stats.books?.map(b => ({ topic: b.topic, bookCount: b.books?.length || 0 }))
        });

        if (!stats.books || stats.books.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i data-lucide="book-open" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                    <p class="text-lg font-medium text-gray-600 mb-2">Henüz kitap yüklenmemiş</p>
                    <p class="text-sm">PDF dosyalarınızı yükleyerek başlayın</p>
                </div>
            `;
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
            return;
        }

        const booksHTML = `
            <div class="flex items-center justify-between mb-3">
                <div class="text-sm text-gray-600">Toplam konu: ${stats.books.length}</div>
                <button onclick="deleteAllBooks()" class="text-sm text-red-600 hover:text-red-800 flex items-center px-3 py-1 border border-red-200 rounded hover:bg-red-50">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                    Hepsini Sil
                </button>
            </div>
            ` + stats.books.map(topic => `
            <div class="border border-gray-200 rounded-lg p-4 card-hover">
                <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                    <i data-lucide="folder" class="w-5 h-5 mr-2 text-blue-600"></i>
                    ${topic.topic.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    <span class="ml-2 text-sm text-gray-500">(${topic.book_count || topic.books.length} kitap)</span>
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    ${topic.books.map(book => `
                        <div class="bg-gray-50 rounded p-3 hover:bg-gray-100 transition-colors duration-200 group">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h5 class="font-medium text-gray-800">${book.name}</h5>
                                    <p class="text-sm text-gray-600 mt-1">
                                        ${book.file_count || book.files.length} dosya
                                        ${book.total_size_mb ? ` • ${book.total_size_mb.toFixed(1)} MB` : ''}
                                    </p>
                                    <p class="text-xs text-gray-500 mt-1">
                                        ${book.files ? book.files.slice(0, 2).join(', ') : ''}
                                        ${book.files && book.files.length > 2 ? '...' : ''}
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                    <button onclick="deleteBook('${topic.topic}', '${book.name}')" 
                                            class="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-all duration-200"
                                            title="Kitabı sil">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');

        container.innerHTML = booksHTML;
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }
    } catch (error) {
        console.error('Kitap yükleme hatası:', error);
        if (container) {
            container.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4"></i>
                    <p>Kitaplar yüklenirken hata oluştu</p>
                    <button onclick="forceRefreshBooks()" class="mt-2 text-sm text-blue-600 hover:text-blue-800">Tekrar Dene</button>
                </div>
            `;
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
        }
    }
}

/**
 * Force refresh books (bypass cache)
 */
async function forceRefreshBooks() {
    console.log('🔄 Force refresh books başlatıldı...');
    
    // DOM hazır mı kontrol et
    if (document.readyState !== 'complete') {
        console.warn('⚠️ DOM henüz hazır değil, bekleniyor...');
        setTimeout(forceRefreshBooks, 100);
        return;
    }
    
    // Elementlerin varlığını kontrol et
    const container = document.getElementById('booksContainer');
    if (!container) {
        console.error('❌ booksContainer bulunamadı, refresh iptal edildi');
        showToast('UI elementi bulunamadı', 'error', 4000);
        return;
    }
    
    try {
        await loadBooks(true);
        showToast('Kitap listesi yenilendi!', 'success', 4000);
        console.log('✅ Books force refresh tamamlandı');
    } catch (error) {
        console.error('❌ Books force refresh hatası:', error);
        showToast('Yenileme hatası: ' + error.message, 'error', 6000);
    }
}

/**
 * Delete book
 * @param {string} topic - Topic name
 * @param {string} bookName - Book name to delete
 */
async function deleteBook(topic, bookName) {
    console.log('🗑️ Kitap silme başlatıldı:', topic, bookName);
    
    const confirmed = await showConfirm(
        `"${bookName}" kitabını silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz ve kitaba ait tüm dosyalar silinecektir.`,
        'Kitabı Sil'
    );
    
    if (!confirmed) {
        console.log('❌ Kullanıcı silme işlemini iptal etti');
        return;
    }

    try {
        showLoading('Kitap siliniyor...');
        console.log('📡 API çağrısı gönderiliyor...');

        const response = await fetch('/api/delete-book', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                topic: topic, 
                book_name: bookName 
            })
        });

        const result = await response.json();
        console.log('📡 API yanıtı:', response.status, result);

        if (response.ok) {
            showToast(result.message, 'success', 5000);
            console.log('🔄 UI güncellemesi başlatılıyor...');
            
            await refreshStats(); // Stats güncelle
            await loadBooks(true); // Kitap listesini yenile
            if (typeof loadTopics === 'function') {
                await loadTopics(); // Topic listesini yenile
            }
            
            console.log('✅ UI güncellendi');
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showToast('Silme hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

/**
 * Delete all books
 */
async function deleteAllBooks() {
    const confirmed = await showConfirm(
        'Tüm kitaplar ve konu klasörleri silinecek. Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
        'Hepsini Sil'
    );
    if (!confirmed) return;

    try {
        showLoading('Tüm kitaplar siliniyor...');
        const response = await fetch('/api/delete-all-books', { method: 'POST' });
        const result = await response.json();
        if (response.ok) {
            showToast(result.message || 'Tüm kitaplar silindi', 'success', 6000);
            await refreshStats();
            await loadBooks(true);
            if (typeof loadTopics === 'function') {
                await loadTopics();
            }
        } else {
            throw new Error(result.error || 'Silme hatası');
        }
    } catch (error) {
        showToast('Silme hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

// Make functions global
window.loadBooks = loadBooks;
window.forceRefreshBooks = forceRefreshBooks;
window.deleteBook = deleteBook;
window.deleteAllBooks = deleteAllBooks;
