{% extends "base.html" %}

{% block title %}{{ path.title.tr }} - Öğrenme Patikası{% endblock %}

{% block content %}
<!-- Path Header -->
<div class="bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg p-8 mb-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold mb-2">{{ path.title.tr }}</h1>
        <p class="text-xl opacity-90 mb-4">{{ path.subtitle.tr }}</p>
        <div class="flex items-center space-x-6 text-sm">
            <div class="flex items-center">
                <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                {{ path.estimatedMinutes }} dakika
            </div>
            <div class="flex items-center">
                <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                {{ chapters|length }} bölüm
            </div>
            <div class="flex items-center">
                <i data-lucide="tag" class="w-4 h-4 mr-2"></i>
                {{ path.tags|join(', ') }}
            </div>
        </div>
    </div>
</div>

<!-- Chapters -->
<div class="max-w-4xl mx-auto">
    {% for chapter in chapters %}
    <div class="mb-8 bg-white shadow rounded-lg overflow-hidden">
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-medium mr-3">
                        {{ loop.index }}
                    </span>
                    {{ chapter.title }}
                </h2>
                <div class="flex items-center text-sm text-gray-500">
                    <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                    {{ chapter.readingMinutes }} dk
                </div>
            </div>
            {% if chapter.summary %}
            <p class="mt-2 text-gray-600">{{ chapter.summary }}</p>
            {% endif %}
        </div>
        
        <div class="px-6 py-6">
            <div class="prose max-w-none">
                {{ chapter.content | safe }}
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Navigation -->
<div class="max-w-4xl mx-auto mt-8 text-center">
    <a href="/" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        Ana Sayfaya Dön
    </a>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Markdown content'i basit HTML'e çevir
    document.addEventListener('DOMContentLoaded', function() {
        const proseElements = document.querySelectorAll('.prose');
        proseElements.forEach(element => {
            let content = element.innerHTML;
            
            // Basit markdown to HTML dönüşümleri
            content = content.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-gray-800 mb-3 mt-6">$1</h3>');
            content = content.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-gray-800 mb-4 mt-8">$1</h2>');
            content = content.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-gray-900 mb-4 mt-8">$1</h1>');
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>');
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            content = content.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="text-indigo-600 hover:text-indigo-800 underline">$1</a>');
            
            // Paragrafları çevir
            const paragraphs = content.split('\n\n');
            content = paragraphs.map(p => {
                p = p.trim();
                if (p && !p.startsWith('<h') && !p.startsWith('<ul') && !p.startsWith('<ol')) {
                    return `<p class="mb-4 text-gray-700 leading-relaxed">${p}</p>`;
                }
                return p;
            }).join('\n');
            
            element.innerHTML = content;
        });
        
        lucide.createIcons();
    });
</script>
{% endblock %}
