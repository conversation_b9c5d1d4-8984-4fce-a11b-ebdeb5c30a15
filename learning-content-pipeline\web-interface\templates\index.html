{% extends "base.html" %}

{% block title %}RAG Öğrenme Sistemi - Ana Say<PERSON>{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg card-hover">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i data-lucide="database" class="h-8 w-8 text-indigo-600"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Toplam Metin Parçası</dt>
                        <dd class="text-lg font-medium text-gray-900" id="totalChunks">-</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg card-hover">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i data-lucide="book-open" class="h-8 w-8 text-green-600"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Yüklenen Kitaplar</dt>
                        <dd class="text-lg font-medium text-gray-900" id="totalBooks">-</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg card-hover">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i data-lucide="map" class="h-8 w-8 text-purple-600"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Öğrenme Patikaları</dt>
                        <dd class="text-lg font-medium text-gray-900" id="totalPaths">-</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- PDF Upload Section -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                <i data-lucide="upload" class="w-5 h-5 mr-2 text-indigo-600"></i>
                PDF Yükle
            </h3>
            <p class="mt-1 text-sm text-gray-600">
                Öğrenme patikalarınız için PDF kitaplarınızı yükleyin
            </p>
        </div>
        <div class="px-6 py-6">
            <form id="uploadForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <label for="topic" class="block text-sm font-medium text-gray-700">Konu</label>
                        <input type="text" id="topic" name="topic" placeholder="Konu adı yazın veya seçin..." class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" autocomplete="off">
                        <div id="topicDropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                            <!-- Dropdown items will be populated here -->
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Önceki konulardan seçin veya yeni konu adı yazın</p>
                    </div>
                    <div>
                        <label for="bookName" class="block text-sm font-medium text-gray-700">
                            Kitap Adı 
                            <span class="text-xs text-gray-500">(tek dosya için zorunlu)</span>
                        </label>
                        <input type="text" id="bookName" name="bookName" placeholder="Tek dosya için: Örn: Osmanlı Tarihi" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        <p class="text-xs text-gray-500 mt-1">Çoklu dosya yüklemede her dosya kendi adını kullanır</p>
                    </div>
                </div>
                

                <!-- Drag & Drop Area -->
                <div id="dropArea" class="upload-area mt-4 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-indigo-400 hover:bg-gray-50 transition-all duration-200">
                    <div class="space-y-2 text-center">
                        <i data-lucide="file-plus" class="mx-auto h-12 w-12 text-gray-400"></i>
                        <div class="flex text-sm text-gray-600">
                            <label for="fileInput" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                <span>PDF dosyası seçin</span>
                                <input id="fileInput" name="file" type="file" accept=".pdf" multiple class="sr-only">
                            </label>
                            <p class="pl-1">veya buraya sürükleyip bırakın (çoklu seçim)</p>
                        </div>
                        <p class="text-xs text-gray-500">PDF formatında, maksimum 100MB (çoklu seçim yapabilirsiniz)</p>
                    </div>
                </div>

                <!-- Selected Files Display -->
                <div id="selectedFilesDiv" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start justify-between">
                        <div class="flex items-start">
                            <i data-lucide="files" class="h-8 w-8 text-blue-600 mt-1"></i>
                            <div class="ml-4 flex-1">
                                <p class="text-sm font-medium text-blue-900 mb-2">Seçilen Dosyalar:</p>
                                <div id="selectedFilesList" class="space-y-1">
                                    <!-- Files will be listed here -->
                                </div>
                                <p class="text-xs text-blue-700 mt-2" id="selectedFilesTotalSize"></p>
                            </div>
                        </div>
                        <button type="button" onclick="clearSelectedFiles()" class="ml-4 text-blue-600 hover:text-blue-800">
                            <i data-lucide="x" class="h-5 w-5"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                    <i data-lucide="upload-cloud" class="w-4 h-4 mr-2"></i>
                    PDF'leri Yükle
                </button>
            </form>
        </div>
    </div>

    <!-- Learning Path Creation -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                <i data-lucide="brain" class="w-5 h-5 mr-2 text-purple-600"></i>
                Öğrenme Patikası Oluştur
            </h3>
            <p class="mt-1 text-sm text-gray-600">
                AI ile özel öğrenme patikaları oluşturun
            </p>
        </div>
        <div class="px-6 py-6">
            <form id="createPathForm" class="space-y-4">
                <div class="relative">
                    <label for="pathTopic" class="block text-sm font-medium text-gray-700">Hangi konuda patika oluşturmak istiyorsunuz?</label>
                    <input type="text" id="pathTopic" name="pathTopic" placeholder="Konu adı yazın veya seçin..." class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" autocomplete="off">
                    <div id="pathTopicDropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                        <!-- Dropdown items will be populated here -->
                    </div>
                </div>
                
                <div>
                    <label for="pathLanguage" class="block text-sm font-medium text-gray-700 mb-3">Dil Seçimi</label>
                    
                    <!-- Tek Dil / Çoklu Dil Toggle -->
                    <div class="mb-4">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="languageMode" value="single" checked class="h-4 w-4 text-purple-600 border-gray-300 focus:ring-purple-500">
                                <span class="ml-2 text-sm text-gray-700">Tek Dil</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="languageMode" value="multiple" class="h-4 w-4 text-purple-600 border-gray-300 focus:ring-purple-500">
                                <span class="ml-2 text-sm text-gray-700">Çoklu Dil</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="languageMode" value="all" class="h-4 w-4 text-purple-600 border-gray-300 focus:ring-purple-500">
                                <span class="ml-2 text-sm text-gray-700">Tüm Diller</span>
                            </label>
                        </div>
                    </div>

                    <!-- Tek Dil Seçimi -->
                    <div id="singleLanguageSelect" class="mb-3">
                        <select id="pathLanguage" name="pathLanguage" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md">
                            <option value="tr">🇹🇷 Türkçe</option>
                            <option value="en">🇺🇸 İngilizce</option>
                            <option value="de">🇩🇪 Almanca</option>
                            <option value="it">🇮🇹 İtalyanca</option>
                            <option value="ru">🇷🇺 Rusça</option>
                            <option value="fr">🇫🇷 Fransızca</option>
                            <option value="es">🇪🇸 İspanyolca</option>
                            <option value="pt">🇵🇹 Portekizce</option>
                        </select>
                    </div>

                    <!-- Çoklu Dil Seçimi -->
                    <div id="multipleLanguageSelect" class="hidden">
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="tr" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇹🇷 Türkçe</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="en" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇺🇸 İngilizce</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="de" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇩🇪 Almanca</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="it" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇮🇹 İtalyanca</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="ru" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇷🇺 Rusça</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="fr" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇫🇷 Fransızca</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="es" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇪🇸 İspanyolca</span>
                            </label>
                            <label class="flex items-center p-2 rounded hover:bg-gray-50">
                                <input type="checkbox" value="pt" class="language-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <span class="ml-2 text-sm">🇵🇹 Portekizce</span>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">İstediğiniz dilleri seçin</p>
                    </div>

                    <!-- Tüm Diller Uyarısı -->
                    <div id="allLanguageWarning" class="hidden bg-yellow-50 border-l-4 border-yellow-400 p-3 mt-3">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    <strong>8 dilde</strong> aynı patika oluşturulacak. Bu işlem uzun sürebilir.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                    <i data-lucide="sparkles" class="w-4 h-4 mr-2"></i>
                    Patika Oluştur
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Existing Books -->
<div class="mt-8 bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
                <i data-lucide="library" class="w-5 h-5 mr-2 text-green-600"></i>
                Mevcut Kitaplar
            </h3>
            <button onclick="forceRefreshBooks()" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                <i data-lucide="refresh-cw" class="w-4 h-4 mr-1"></i>
                Yenile
            </button>
        </div>
    </div>
    <div class="px-6 py-4">
        <div id="booksContainer" class="space-y-4">
            <!-- Books will be loaded here -->
        </div>
    </div>
</div>

<!-- Learning Paths -->
<div class="mt-8 bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <i data-lucide="route" class="w-5 h-5 mr-2 text-blue-600"></i>
            Oluşturulan Öğrenme Patikaları
        </h3>
    </div>
    <div class="px-6 py-4">
        <div id="pathsContainer" class="space-y-4">
            <!-- Learning paths will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Load modular JavaScript files -->
<script src="{{ url_for('static', filename='js/utils.js') }}"></script>
<script src="{{ url_for('static', filename='js/upload.js') }}"></script>
<script src="{{ url_for('static', filename='js/books.js') }}"></script>
<script src="{{ url_for('static', filename='js/paths.js') }}"></script>
<script src="{{ url_for('static', filename='js/autocomplete.js') }}"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %}
