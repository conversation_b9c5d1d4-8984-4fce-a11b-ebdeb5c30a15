// ===== UTILITY FUNCTIONS =====

/**
 * Modern Toast notification system with animations and stacking
 * @param {string} message - Message to display
 * @param {string} type - Type of toast (info, success, error, warning)
 * @param {number} duration - Auto-dismiss duration in milliseconds (default: 5000)
 */
function showToast(message, type = 'info', duration = 5000) {
    console.log(`📢 Toast [${type.toUpperCase()}]: ${message}`);
    
    // Create or get toast container
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm';
        document.body.appendChild(container);
    }
    
    // Toast configuration
    const toastConfig = {
        success: {
            icon: '✓',
            bgColor: 'bg-green-500',
            textColor: 'text-white',
            borderColor: 'border-green-600',
            iconBg: 'bg-green-600'
        },
        error: {
            icon: '✕',
            bgColor: 'bg-red-500',
            textColor: 'text-white',
            borderColor: 'border-red-600',
            iconBg: 'bg-red-600'
        },
        warning: {
            icon: '⚠',
            bgColor: 'bg-yellow-500',
            textColor: 'text-white',
            borderColor: 'border-yellow-600',
            iconBg: 'bg-yellow-600'
        },
        info: {
            icon: 'ℹ',
            bgColor: 'bg-blue-500',
            textColor: 'text-white',
            borderColor: 'border-blue-600',
            iconBg: 'bg-blue-600'
        }
    };
    
    const config = toastConfig[type] || toastConfig.info;
    const toastId = 'toast-' + Date.now() + Math.random().toString(36).substr(2, 9);
    
    // Create toast element
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast-notification transform translate-x-full transition-all duration-300 ease-in-out ${config.bgColor} ${config.textColor} ${config.borderColor} border-l-4 p-4 rounded-lg shadow-lg max-w-sm`;
    
    toast.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center w-6 h-6 ${config.iconBg} rounded-full text-xs font-bold">
                    ${config.icon}
                </div>
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium break-words">${message}</p>
            </div>
            <div class="ml-4">
                <button onclick="dismissToast('${toastId}')" class="text-current opacity-70 hover:opacity-100 transition-opacity duration-200">
                    <span class="sr-only">Kapat</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;
    
    // Add to container
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
        toast.classList.add('translate-x-0');
    }, 100);
    
    // Auto dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            dismissToast(toastId);
        }, duration);
    }
    
    return toastId;
}

/**
 * Dismiss a specific toast notification
 * @param {string} toastId - ID of the toast to dismiss
 */
function dismissToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        // Animate out
        toast.classList.remove('translate-x-0');
        toast.classList.add('translate-x-full');
        
        // Remove from DOM after animation
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            
            // Clean up empty container
            const container = document.getElementById('toast-container');
            if (container && container.children.length === 0) {
                container.parentNode.removeChild(container);
            }
        }, 300);
    }
}

/**
 * Clear all toast notifications
 */
function clearAllToasts() {
    const container = document.getElementById('toast-container');
    if (container) {
        const toasts = container.querySelectorAll('.toast-notification');
        toasts.forEach(toast => {
            dismissToast(toast.id);
        });
    }
}

/**
 * Modern confirm dialog replacement
 * @param {string} message - Confirmation message
 * @param {string} title - Dialog title (optional)
 * @returns {Promise<boolean>} Promise that resolves to user choice
 */
function showConfirm(message, title = 'Onay Gerekli') {
    return new Promise((resolve) => {
        // Create modal backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4 animate-fade-in';
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'bg-white rounded-lg shadow-xl max-w-md w-full transform transition-all duration-200 scale-95';
        
        modal.innerHTML = `
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.064 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                </div>
                <p class="text-gray-700 mb-6 leading-relaxed">${message}</p>
                <div class="flex space-x-3 justify-end">
                    <button id="confirm-cancel" class="px-4 py-2 text-gray-600 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors duration-200">
                        İptal
                    </button>
                    <button id="confirm-ok" class="px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-200">
                        Onayla
                    </button>
                </div>
            </div>
        `;
        
        backdrop.appendChild(modal);
        document.body.appendChild(backdrop);
        
        // Animate in
        setTimeout(() => {
            modal.classList.remove('scale-95');
            modal.classList.add('scale-100');
        }, 50);
        
        // Event handlers
        const cleanup = (result) => {
            modal.classList.add('scale-95');
            backdrop.classList.add('opacity-0');
            setTimeout(() => {
                document.body.removeChild(backdrop);
                resolve(result);
            }, 200);
        };
        
        document.getElementById('confirm-ok').onclick = () => cleanup(true);
        document.getElementById('confirm-cancel').onclick = () => cleanup(false);
        
        // ESC key support
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleKeydown);
                cleanup(false);
            }
        };
        document.addEventListener('keydown', handleKeydown);
        
        // Click outside to cancel
        backdrop.onclick = (e) => {
            if (e.target === backdrop) {
                cleanup(false);
            }
        };
    });
}

// Make functions globally accessible
window.dismissToast = dismissToast;
window.clearAllToasts = clearAllToasts;
window.showConfirm = showConfirm;

/**
 * Show loading overlay with message
 * @param {string} message - Loading message
 */
function showLoading(message = 'Yükleniyor...') {
    console.log('🔄 Loading:', message);
    
    // Prefer base.html modal if present
    const modal = document.getElementById('loadingModal');
    const modalText = document.getElementById('loadingText');
    if (modal && modalText) {
        modalText.textContent = message;
        modal.classList.remove('hidden');
        return;
    }
    
    // Fallback: remove any existing overlay we created
    const existingLoading = document.querySelector('.loading-overlay');
    if (existingLoading) existingLoading.remove();
    
    // Create fallback overlay with always-animated spinner
    const loading = document.createElement('div');
    loading.className = 'loading-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loading.innerHTML = `
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <div class="flex flex-col items-center space-y-4">
                <div class="loading-spinner rounded-full h-12 w-12 mb-2"></div>
                <div class="space-y-2">
                    <h3 class="text-lg font-semibold text-gray-900">İşlem Devam Ediyor</h3>
                    <p class="text-gray-600 text-sm">${message}</p>
                    <div class="mt-3 text-xs text-gray-500">
                        <p>Bu işlem birkaç dakika sürebilir...</p>
                        <p>Lütfen sayfayı kapatmayın</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(loading);
}

/**
 * Hide loading overlay
 */
function hideLoading() {
    console.log('✅ Loading finished');
    // Prefer base.html modal if present
    const modal = document.getElementById('loadingModal');
    if (modal) {
        modal.classList.add('hidden');
    }
    // Also clean up fallback overlay if any
    const loading = document.querySelector('.loading-overlay');
    if (loading) {
        loading.remove();
    }
}

/**
 * Enhanced API call helper with comprehensive error handling and retry logic
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options and retry configuration
 * @returns {Promise} API response data
 */
async function apiCall(url, options = {}) {
    const {
        retries = 2,
        retryDelay = 1000,
        timeout = 30000,
        showErrorToast = true,
        ...fetchOptions
    } = options;

    // Add timeout to abort long-running requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                console.log(`🌐 API call: ${url} (attempt ${attempt + 1})`);
                
                const response = await fetch(url, {
                    ...fetchOptions,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                // Handle different response types
                let data;
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    data = { text: await response.text() };
                }

                // Handle HTTP errors
                if (!response.ok) {
                    const errorMessage = data.error || data.message || `HTTP ${response.status}: ${response.statusText}`;
                    
                    // Don't retry client errors (4xx)
                    if (response.status >= 400 && response.status < 500) {
                        throw new APIError(errorMessage, response.status, false);
                    }
                    
                    // Retry server errors (5xx) and network issues
                    throw new APIError(errorMessage, response.status, true);
                }

                console.log(`✅ API success: ${url}`);
                return data;

            } catch (error) {
                clearTimeout(timeoutId);

                // Handle specific error types
                if (error.name === 'AbortError') {
                    throw new APIError('İstek zaman aşımına uğradı', 408, false);
                }

                if (error instanceof TypeError && error.message.includes('fetch')) {
                    throw new APIError('Ağ bağlantı hatası. İnternet bağlantınızı kontrol edin.', 0, true);
                }

                // If this is the last attempt or error shouldn't be retried
                if (attempt === retries || (error instanceof APIError && !error.retryable)) {
                    if (showErrorToast && !(error instanceof APIError && error.status === 429)) {
                        // Don't show toast for rate limit errors (handled elsewhere)
                        showToast(error.message || 'Beklenmeyen bir hata oluştu', 'error', 8000);
                    }
                    throw error;
                }

                // Wait before retry (exponential backoff)
                const delay = retryDelay * Math.pow(2, attempt);
                console.log(`⏳ Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    } catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
}

/**
 * Custom API Error class for better error handling
 */
class APIError extends Error {
    constructor(message, status = 0, retryable = false) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.retryable = retryable;
    }
}

/**
 * Global error handler for unhandled promise rejections
 */
function setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        console.error('🔥 Unhandled promise rejection:', event.reason);
        
        // Prevent the default browser behavior
        event.preventDefault();
        
        // Show user-friendly error message
        const errorMessage = event.reason instanceof Error 
            ? event.reason.message 
            : 'Beklenmeyen bir hata oluştu';
            
        showToast(errorMessage, 'error', 6000);
    });

    // Handle JavaScript errors
    window.addEventListener('error', function(event) {
        console.error('🔥 JavaScript error:', event.error);
        
        // Don't show toast for every JS error to avoid spam
        const criticalErrors = [
            'ChunkLoadError',
            'Loading chunk',
            'Loading CSS chunk'
        ];
        
        const errorMessage = event.error?.message || event.message || '';
        const isCritical = criticalErrors.some(pattern => errorMessage.includes(pattern));
        
        if (isCritical) {
            showToast('Sayfa yüklenirken hata oluştu. Sayfayı yenileyin.', 'error', 10000);
        }
    });
}

/**
 * Retry mechanism for failed operations
 * @param {Function} operation - Function to retry
 * @param {Object} options - Retry options
 * @returns {Promise} Operation result
 */
async function withRetry(operation, options = {}) {
    const {
        maxAttempts = 3,
        delay = 1000,
        exponentialBackoff = true,
        onRetry = null
    } = options;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await operation();
        } catch (error) {
            if (attempt === maxAttempts) {
                throw error;
            }

            const retryDelay = exponentialBackoff ? delay * Math.pow(2, attempt - 1) : delay;
            
            if (onRetry) {
                onRetry(error, attempt, retryDelay);
            }
            
            console.log(`⏳ Retrying operation (attempt ${attempt + 1}/${maxAttempts}) in ${retryDelay}ms`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
}

/**
 * Safe function execution with error boundaries
 * @param {Function} fn - Function to execute safely
 * @param {string} context - Context description for error logging
 * @param {*} fallbackValue - Value to return on error
 */
async function safeExecute(fn, context = 'operation', fallbackValue = null) {
    try {
        return await fn();
    } catch (error) {
        console.error(`🛡️ Safe execution failed in ${context}:`, error);
        
        // Log detailed error information for debugging
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            context: context,
            timestamp: new Date().toISOString()
        });
        
        return fallbackValue;
    }
}

/**
 * Format file size in human readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Refresh system statistics
 */
async function refreshStats() {
    try {
        const stats = await apiCall('/api/status');
        
        document.getElementById('totalChunks').textContent = stats.total_chunks || 0;
        const totalBooks = stats.books ? stats.books.reduce((sum, topic) => sum + topic.books.length, 0) : 0;
        document.getElementById('totalBooks').textContent = totalBooks;
        document.getElementById('totalPaths').textContent = stats.learning_paths?.length || 0;
        
        console.log('📊 Stats updated:', stats);
    } catch (error) {
        console.error('Stats refresh error:', error);
    }
}

// Export functions for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showToast,
        showLoading,
        hideLoading,
        apiCall,
        formatFileSize,
        refreshStats
    };
}
