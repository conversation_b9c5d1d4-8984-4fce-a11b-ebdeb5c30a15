"""
RAG System module for the learning platform
"""
import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_ollama import OllamaEmbeddings, OllamaLLM
from langchain_google_genai import GoogleGenerative<PERSON>IEmbeddings, ChatGoogleGenerativeAI
from langchain_google_vertexai import VertexAIEmbeddings, ChatVertexAI
from langchain_chroma import Chroma

from config import MODEL_CONFIG, TEXT_PROCESSING_CONFIG, DB_FOLDER, OUTPUTS_FOLDER, RAG_SELECTION_CONFIG
from utils import extract_and_parse_json, get_books_simple

logger = logging.getLogger(__name__)

class WebRAGSystem:
    """Web interface RAG system"""
    
    def __init__(self) -> None:
        """
        Initialize the RAG system with model configurations.
        
        Sets up embeddings, LLM, vector store, and text processing components
        based on the configuration settings.
        """
        self.model_name: str = MODEL_CONFIG['llm_model']
        self.embedding_model: str = MODEL_CONFIG['embedding_model']
        self.persist_directory: str = str(DB_FOLDER)
        self.chunk_size: int = TEXT_PROCESSING_CONFIG['chunk_size']
        self.chunk_overlap: int = TEXT_PROCESSING_CONFIG['chunk_overlap']
        
        # Initialize as None - will be set up in _initialize()
        self.embeddings: Optional[Any] = None
        self.llm: Optional[Any] = None
        self.vector_store: Optional[Chroma] = None
        self.text_splitter: Optional[RecursiveCharacterTextSplitter] = None
        
        self._initialize()
    
    def _initialize(self) -> None:
        """
        Initialize system components including embeddings, LLM, and vector store.
        
        This method sets up:
        - Gemini or Ollama embeddings based on configuration
        - Gemini or Ollama LLM with temperature and context settings
        - Text splitter for document chunking
        - ChromaDB vector store for similarity search
        
        Raises:
            Exception: If any component fails to initialize
        """
        try:
            # Initialize embeddings based on provider
            embedding_provider = MODEL_CONFIG.get('embedding_provider', 'ollama')
            if embedding_provider == 'gemini':
                logger.info(f"Starting Vertex AI Gemini embedding model: {self.embedding_model}")
                
                # Set up authentication
                import os
                credentials_path = MODEL_CONFIG.get('google_application_credentials')
                if credentials_path and os.path.exists(credentials_path):
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
                    logger.info(f"Using service account credentials: {credentials_path}")
                else:
                    logger.warning("No service account credentials found. Using default authentication.")
                
                self.embeddings = VertexAIEmbeddings(
                    model_name=self.embedding_model,
                    project=MODEL_CONFIG.get('vertex_project_id', 'your-project-id'),
                    location=MODEL_CONFIG.get('vertex_location', 'us-central1')
                )
            else:
                logger.info(f"Starting Ollama embedding model: {self.embedding_model}")
                self.embeddings = OllamaEmbeddings(
                    model=self.embedding_model,
                    base_url=MODEL_CONFIG['ollama_base_url']
                )
            
            # Initialize LLM based on provider
            llm_provider = MODEL_CONFIG.get('llm_provider', 'ollama')
            if llm_provider == 'gemini':
                logger.info(f"Starting Vertex AI Gemini LLM: {self.model_name}")
                
                # Set up authentication (if not already done)
                import os
                credentials_path = MODEL_CONFIG.get('google_application_credentials')
                if credentials_path and os.path.exists(credentials_path):
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
                    logger.info(f"Using service account credentials: {credentials_path}")
                else:
                    logger.warning("No service account credentials found. Using default authentication.")
                
                self.llm = ChatVertexAI(
                    model_name=self.model_name,
                    project=MODEL_CONFIG.get('vertex_project_id', 'your-project-id'),
                    location=MODEL_CONFIG.get('vertex_location', 'us-central1'),
                    temperature=MODEL_CONFIG['temperature'],
                    top_p=MODEL_CONFIG['top_p']
                )
            else:
                logger.info(f"Starting Ollama LLM: {self.model_name}")
                self.llm = OllamaLLM(
                    model=self.model_name,
                    temperature=MODEL_CONFIG['temperature'],
                    top_p=MODEL_CONFIG['top_p'],
                    num_ctx=MODEL_CONFIG['num_ctx']
                )
            
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                length_function=len,
                separators=TEXT_PROCESSING_CONFIG['separators']
            )
            
            self._load_or_create_vectorstore()
            logger.info("RAG system successfully initialized")
            
        except Exception as e:
            logger.error(f"RAG system initialization error: {e}")
            raise
    
    def _load_or_create_vectorstore(self):
        """Load or create vector database"""
        try:
            self.vector_store = Chroma(
                persist_directory=self.persist_directory,
                embedding_function=self.embeddings
            )
            logger.info(f"Vector database loaded: {self.persist_directory}")
        except Exception as e:
            logger.error(f"Vector database error: {e}")
            raise
    
    def add_pdf_to_system(self, pdf_path: str, topic: str, book_name: str) -> Dict[str, Any]:
        """Add PDF to system"""
        try:
            logger.info(f"Processing PDF: {pdf_path}")
            
            # Load PDF
            loader = PyPDFLoader(pdf_path)
            documents = loader.load()
            
            if not documents:
                return {"success": False, "error": "Could not extract text from PDF"}
            
            # Add metadata
            for doc in documents:
                doc.metadata.update({
                    "topic": topic,
                    "book": book_name,
                    "file": os.path.basename(pdf_path),
                    "source_type": "book",
                    "upload_date": datetime.now().isoformat()
                })
            
            # Split texts
            texts = self.text_splitter.split_documents(documents)
            
            # Add to vector database
            self.vector_store.add_documents(texts)
            
            logger.info(f"PDF successfully added: {len(texts)} chunks created")
            
            return {
                "success": True,
                "chunks_added": len(texts),
                "total_documents": len(documents)
            }
            
        except Exception as e:
            logger.error(f"PDF addition error: {e}")
            return {"success": False, "error": str(e)}
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics"""
        try:
            collection = self.vector_store._collection
            count = collection.count() if collection else 0
            
            # Get book list directly
            books = get_books_simple()
            
            # List existing learning paths
            learning_paths = []
            manifest_file = OUTPUTS_FOLDER / "manifest.json"
            if manifest_file.exists():
                try:
                    with open(manifest_file, 'r', encoding='utf-8') as f:
                        manifest = json.load(f)
                        learning_paths.append(manifest)
                except Exception:
                    pass
            
            return {
                "total_chunks": count,
                "books": books,
                "learning_paths": learning_paths,
                "model_status": self._check_ollama_status()
            }
            
        except Exception as e:
            logger.error(f"Statistics retrieval error: {e}")
            return {"error": str(e)}
    
    def _check_ollama_status(self) -> bool:
        """Check model status (Ollama or Gemini)"""
        try:
            _ = self.llm.invoke("test")
            return True
        except Exception:
            return False
    
    def create_learning_path(self, topic: str, languages: List[str] = None) -> Dict[str, Any]:
        """Create multi-language learning path"""
        try:
            # Default language list
            if languages is None:
                languages = ["tr"]
            
            logger.info(f"Creating learning path: {topic} - Languages: {languages}")
            
            # Get real uploaded sources
            topic_books = self._get_actual_sources_for_topic(topic)
            
            # Ask model for advanced chapter plan
            planning_prompt = self._generate_planning_prompt(topic, topic_books)
            
            # Create plan with timeout and specific error handling
            try:
                plan_response = self.llm.invoke(planning_prompt)
                # Extract content from AIMessage object if it exists
                if hasattr(plan_response, 'content'):
                    plan_response_text = plan_response.content
                else:
                    plan_response_text = str(plan_response)
                logger.info(f"LLM response: {plan_response_text[:200]}...")
            except (ConnectionError, TimeoutError) as e:
                logger.error(f"LLM connection/timeout error during planning: {e}")
                plan_response_text = ""
            except ValueError as e:
                logger.error(f"LLM input validation error during planning: {e}")
                plan_response_text = ""
            except Exception as e:
                logger.error(f"Unexpected error during LLM planning: {type(e).__name__}: {e}")
                plan_response_text = ""
            
            # Extract and parse JSON
            plan_data = extract_and_parse_json(plan_response_text)
            chapters = plan_data.get("chapters", [])
            
            learning_path = {
                "pathId": topic.lower().replace(" ", "-"),
                "title": {language: topic for language in languages},
                "subtitle": {language: f"{topic} hakkında kapsamlı öğrenme patikası" for language in languages},
                "estimatedMinutes": sum(ch.get('estimated_minutes', 15) for ch in chapters),
                "tags": [topic.lower()],
                "chapters": []
            }
            
            # Create path for each language
            all_results = []
            for language in languages:
                logger.info(f"📝 Creating content in {language.upper()}...")
                
                # Create content for each chapter
                language_learning_path = learning_path.copy()
                language_learning_path["chapters"] = []
                language_learning_path["pathId"] = f"{topic.lower().replace(' ', '-')}-{language}"
                language_learning_path["language"] = language
                
                for i, chapter in enumerate(chapters, 1):
                    chapter_content = self._create_chapter_content(topic, chapter, i, topic_books, language)
                    if chapter_content:
                        language_learning_path["chapters"].append(chapter_content)
                
                all_results.append(language_learning_path)
                logger.info(f"✅ {language.upper()} completed - {len(language_learning_path['chapters'])} chapters")
            
            # Save manifest
            manifest_file = OUTPUTS_FOLDER / "manifest.json"
            with open(manifest_file, 'w', encoding='utf-8') as f:
                json.dump(learning_path, f, ensure_ascii=False, indent=2)
            
            # Return primary result for UI (first language)
            primary_result = all_results[0] if all_results else learning_path
            
            return {
                "success": True, 
                "learning_path": primary_result,
                "total_languages": len(languages),
                "created_languages": [path["language"] for path in all_results]
            }
            
        except Exception as e:
            logger.error(f"Learning path creation error: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_actual_sources_for_topic(self, topic: str) -> List[str]:
        """Get real sources for specified topic"""
        try:
            # Get all metadata from vector store
            all_docs = self.vector_store._collection.get()
            
            # Collect source information - ONLY for this topic
            topic_sources = set()
            all_sources = set()
            
            if 'metadatas' in all_docs and all_docs['metadatas']:
                for metadata in all_docs['metadatas']:
                    if metadata and 'source' in metadata:
                        source = metadata['source']
                        all_sources.add(source)
                        
                        # Get topic directly from metadata (not from path!)
                        metadata_topic = metadata.get('topic', '')
                        
                        # Simple case-insensitive match
                        if metadata_topic.lower() == topic.lower():
                            topic_sources.add(source)
                            logger.info(f"✅ Topic match: {source} -> {metadata_topic}")
                        else:
                            logger.info(f"❌ Topic mismatch: {source} -> {metadata_topic} (looking for: {topic})")
            
            # Log results
            logger.info(f"📚 TOTAL {len(all_sources)} sources found")
            logger.info(f"🎯 ONLY {len(topic_sources)} sources selected for '{topic}'")
            
            if topic_sources:
                source_list = list(topic_sources)
                logger.info(f"📋 Sources for topic '{topic}':")
                for i, source in enumerate(source_list, 1):
                    logger.info(f"  {i}. {source}")
                
                return [f"Topic source: {source}" for source in source_list]
            else:
                logger.warning(f"⚠️ No sources found for topic '{topic}'!")
                logger.info(f"💡 Available sources: {list(all_sources)}")
                return [f"No sources found for '{topic}' - please upload PDF files for this topic"]
                
        except Exception as e:
            logger.error(f"❌ Error retrieving source information: {e}")
            return ["Could not retrieve source information"]
    
    def _create_chapter_content(self, main_topic: str, chapter_info: Dict, chapter_num: int, topic_books: List[str], language: str) -> Optional[Dict[str, Any]]:
        """
        Create content for a single chapter.
        
        Args:
            main_topic: The main topic for the learning path
            chapter_info: Dictionary containing chapter metadata (title, summary, etc.)
            chapter_num: Chapter number for ordering
            topic_books: List of books available for this topic
            language: Target language for content generation
            
        Returns:
            Dictionary with chapter metadata or None if creation failed
        """
        try:
            chapter_title = chapter_info['title']
            chapter_summary = chapter_info.get('summary', '')
            
            # Retrieve relevant documents using RAG
            relevant_docs = self._retrieve_relevant_documents(main_topic, chapter_title, chapter_summary)
            
            if not relevant_docs:
                logger.warning(f"No relevant documents found for chapter: {chapter_title}")
                return None
            
            # Build context from retrieved documents
            context = self._build_chapter_context(relevant_docs)
            
            # Generate content using LLM
            content = self._generate_chapter_content(main_topic, chapter_title, chapter_summary, context)
            
            if content:
                # Save chapter to file and return metadata
                return self._save_chapter_file(
                    content, main_topic, chapter_info, chapter_num, language
                )
            
        except Exception as e:
            logger.error(f"Chapter content creation error for '{chapter_title}': {e}")
        
        return None
    
    def _retrieve_relevant_documents(self, main_topic: str, chapter_title: str, chapter_summary: str) -> List[Any]:
        """
        Retrieve relevant documents from vector store for chapter content.
        
        Args:
            main_topic: The main topic for filtering
            chapter_title: Chapter title for semantic search
            chapter_summary: Chapter summary for additional context
            
        Returns:
            List of relevant document objects
        """
        try:
            # Build enriched semantic queries
            query = f"{main_topic} {chapter_title}"
            initial_k = max(10, int(RAG_SELECTION_CONFIG.get('initial_k', 20)))
            use_mmr = bool(RAG_SELECTION_CONFIG.get('use_mmr', True))
            use_multi = bool(RAG_SELECTION_CONFIG.get('use_multi_query', True))
            
            # Generate multiple query variants for better recall
            queries = self._generate_search_queries(query, chapter_title, chapter_summary, main_topic, use_multi)
            
            # Collect documents from all queries with deduplication
            collected_docs = self._collect_documents_from_queries(queries, initial_k, use_mmr)
            
            # Apply topic filtering if configured
            filtered_docs = self._apply_topic_filtering(collected_docs, main_topic)
            
            # Diversify results by source
            diversified_docs = self._diversify_by_source(filtered_docs, main_topic)
            
            logger.info(f"RAG Query: '{query}' - {len(diversified_docs)} documents retrieved")
            return diversified_docs
            
        except Exception as e:
            logger.error(f"Document retrieval error: {e}")
            return []
    
    def _generate_search_queries(self, base_query: str, chapter_title: str, chapter_summary: str, main_topic: str, use_multi: bool) -> List[str]:
        """Generate multiple search queries for better document recall."""
        queries = [base_query]
        
        if use_multi:
            # Add lightweight enriched variants
            if chapter_summary:
                queries.append(chapter_summary.strip()[:120])
            queries.append(f"{chapter_title} önemli olaylar")
            queries.append(f"{main_topic} kronoloji tarih gelişmeler")
        
        return [q for q in queries if q.strip()]
    
    def _collect_documents_from_queries(self, queries: List[str], initial_k: int, use_mmr: bool) -> List[Any]:
        """Collect and deduplicate documents from multiple search queries."""
        collected = []
        seen_hashes = set()
        
        for query in queries:
            try:
                # Use MMR if available and configured for better diversity
                if use_mmr and hasattr(self.vector_store, 'max_marginal_relevance_search'):
                    results = self.vector_store.max_marginal_relevance_search(
                        query, 
                        k=initial_k, 
                        fetch_k=max(initial_k * 2, 50), 
                        lambda_mult=float(RAG_SELECTION_CONFIG.get('mmr_lambda', 0.6))
                    )
                else:
                    results = self.vector_store.similarity_search(query, k=initial_k)
                    
            except (ConnectionError, TimeoutError) as e:
                logger.warning(f"Connection/timeout error for query '{query}': {e}")
                results = self.vector_store.similarity_search(query, k=initial_k)
            except (ValueError, TypeError) as e:
                logger.warning(f"Invalid input for query '{query}': {e}")
                results = []
            except Exception as e:
                logger.warning(f"Unexpected error for query '{query}': {type(e).__name__}: {e}")
                results = self.vector_store.similarity_search(query, k=initial_k)
            
            # Deduplicate results
            for doc in results:
                doc_hash = self._get_document_hash(doc)
                if doc_hash not in seen_hashes:
                    seen_hashes.add(doc_hash)
                    collected.append(doc)
        
        return collected
    
    def _get_document_hash(self, doc: Any) -> str:
        """Generate a unique hash for document deduplication."""
        metadata = getattr(doc, 'metadata', {}) or {}
        doc_id = metadata.get('id')
        if doc_id:
            return str(doc_id)
        
        # Fallback to content + source hash
        content_preview = doc.page_content[:200] if hasattr(doc, 'page_content') else ''
        source = metadata.get('source', 'unknown')
        return str(hash((content_preview, source)))
    
    def _apply_topic_filtering(self, documents: List[Any], main_topic: str) -> List[Any]:
        """Filter documents by topic if configured."""
        filter_by_topic = bool(RAG_SELECTION_CONFIG.get('filter_by_topic', False))
        
        if not filter_by_topic:
            return documents
        
        filtered = []
        for doc in documents:
            metadata = getattr(doc, 'metadata', {}) or {}
            doc_topic = (metadata.get('topic') or '').lower()
            if doc_topic == main_topic.lower():
                filtered.append(doc)
        
        return filtered
    
    def _diversify_by_source(self, documents: List[Any], main_topic: str) -> List[Any]:
        """Diversify documents by ensuring representation from different sources."""
        # First pass: get one document per source
        unique_by_source = {}
        for doc in documents:
            metadata = getattr(doc, 'metadata', {}) or {}
            source = metadata.get('source') or metadata.get('file') or 'unknown'
            if source not in unique_by_source:
                unique_by_source[source] = doc
        
        diversified = list(unique_by_source.values())
        
        # Ensure representation from all available sources if configured
        if bool(RAG_SELECTION_CONFIG.get('ensure_one_per_source', True)):
            diversified = self._ensure_all_sources_represented(diversified, documents, main_topic)
        
        # Apply limits
        max_sources = int(RAG_SELECTION_CONFIG.get('max_sources', 0) or 0)
        if max_sources > 0:
            diversified = diversified[:max_sources]
        else:
            # Hard limit to prevent excessive context
            diversified = diversified[:200]
        
        return diversified
    
    def _ensure_all_sources_represented(self, current_docs: List[Any], all_docs: List[Any], main_topic: str) -> List[Any]:
        """Ensure all available sources for the topic are represented."""
        try:
            # Get all sources available for this topic
            all_docs_meta = self.vector_store._collection.get()
            topic_sources = set()
            filter_by_topic = bool(RAG_SELECTION_CONFIG.get('filter_by_topic', False))
            
            if 'metadatas' in all_docs_meta and all_docs_meta['metadatas']:
                for metadata in all_docs_meta['metadatas']:
                    if not isinstance(metadata, dict):
                        continue
                    source = metadata.get('source') or metadata.get('file')
                    if not source:
                        continue
                    if not filter_by_topic or (metadata.get('topic') or '').lower() == main_topic.lower():
                        topic_sources.add(source)
            
            # Find missing sources and add representatives
            existing_sources = {
                (getattr(doc, 'metadata', {}) or {}).get('source') or 
                (getattr(doc, 'metadata', {}) or {}).get('file') 
                for doc in current_docs
            }
            
            for source in topic_sources:
                if source in existing_sources:
                    continue
                # Find a candidate from all_docs with this source
                candidate = next(
                    (doc for doc in all_docs 
                     if ((getattr(doc, 'metadata', {}) or {}).get('source') == source or 
                         (getattr(doc, 'metadata', {}) or {}).get('file') == source)), 
                    None
                )
                if candidate:
                    current_docs.append(candidate)
                    
        except Exception as e:
            logger.warning(f"Error ensuring source representation: {e}")
        
        return current_docs
    
    def _build_chapter_context(self, relevant_docs: List[Any]) -> str:
        """Build formatted context string from relevant documents."""
        sources_used = set()
        context_parts = []
        
        for i, doc in enumerate(relevant_docs):
            metadata = getattr(doc, 'metadata', {}) or {}
            source = metadata.get('source', f'Source_{i+1}')
            sources_used.add(source)
            
            logger.info(f"Doc {i+1}: {source[:100]}...")
            logger.info(f"Content preview: {doc.page_content[:200]}...")
            
            content = getattr(doc, 'page_content', '')
            context_parts.append(f"[Source: {source}]\n{content}")
        
        logger.info(f"✅ Total {len(sources_used)} different sources used: {list(sources_used)}")
        return "\n\n---\n\n".join(context_parts)
    
    def _generate_chapter_content(self, main_topic: str, chapter_title: str, chapter_summary: str, context: str) -> str:
        """Generate chapter content using LLM with provided context."""
        try:
            content_prompt = self._generate_content_prompt(main_topic, chapter_title, chapter_summary, context)
            raw_content = self.llm.invoke(content_prompt)
            # Extract content from AIMessage object if it exists
            if hasattr(raw_content, 'content'):
                content_text = raw_content.content
            else:
                content_text = str(raw_content)
            return self._sanitize_llm_output(content_text)
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"LLM connection/timeout error during content generation for '{chapter_title}': {e}")
            return ""
        except ValueError as e:
            logger.error(f"LLM input validation error during content generation for '{chapter_title}': {e}")
            return ""
        except MemoryError as e:
            logger.error(f"Memory error during content generation for '{chapter_title}': {e}")
            return ""
        except Exception as e:
            logger.error(f"Unexpected content generation error for '{chapter_title}': {type(e).__name__}: {e}")
            return ""
    
    def _save_chapter_file(self, content: str, main_topic: str, chapter_info: Dict, chapter_num: int, language: str) -> Dict[str, Any]:
        """Save chapter content to file and return metadata."""
        try:
            chapter_title = chapter_info['title']
            chapter_summary = chapter_info.get('summary', '')
            
            # Create output directory
            output_file = OUTPUTS_FOLDER / f"content/{language}/{main_topic.lower().replace(' ', '-')}/chapter{chapter_num:02d}.md"
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Generate front matter
            front_matter = f"""---
pathId: {main_topic.lower().replace(' ', '-')}
chapterId: chapter{chapter_num:02d}
lang: {language}
title: {chapter_title}
summary: "{chapter_summary}"
readingMinutes: {chapter_info.get('estimated_minutes', 15)}
keywords: [{main_topic.lower()}, {chapter_title.lower()}]
---

"""
            
            # Write file
            full_content = front_matter + content
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            return {
                "id": f"chapter{chapter_num:02d}",
                "title": chapter_title,
                "summary": chapter_summary,
                "file": str(output_file.relative_to(OUTPUTS_FOLDER)),
                "readingMinutes": chapter_info.get('estimated_minutes', 15)
            }
            
        except PermissionError as e:
            logger.error(f"Permission denied when saving chapter file: {e}")
            raise
        except OSError as e:
            logger.error(f"File system error when saving chapter file: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error when saving chapter file: {type(e).__name__}: {e}")
            raise
    
    def _generate_planning_prompt(self, topic: str, topic_books: List[str]) -> str:
        """Generate planning prompt for LLM"""
        return f"""
 Sen TARİHÇİ ve EĞİTİM TASARIMCISI gibi davranan bir uzman sistemsin. Her KONUYU kronolojik/dönemsel aşamalara ayırarak öğrenme patikasına dönüştür.

🎯 KONU: "{topic}"
📚 KAYNAKLAR: {chr(10).join(topic_books)}

UNIVERSAL DÖNEMLEŞTİRME ŞABLONLARI:
- Tarihsel: Kuruluş → Yükselme → Zirve → Dönüşüm/Kriz → Miras
- Bilim/Teknik: Temeller → İlk Keşifler → Geliştirme → Uygulamalar → Güncel Durum
- Biyografik: Erken Dönem → İlk Başarılar → Zirve Yılları → Geç Dönem → Miras
- Genel: Arka Plan → Başlangıç → Gelişme → Olgunluk → Güncel/Etki

KURALLAR:
- SADECE kaynaklarda yer alan GERÇEK bilgilere dayan
- Her dönem için belirgin zaman aralığı ve somut unsurlar ver
- Çıktı yalın JSON olsun
- Kapsamlı konular için çok fazla bölüm oluşturma iznin var. Maximum 100 bölüm.

ÇIKTI (JSON):
{{
  "total_chapters": <modelin uygun gördüğü sayı>,
  "chapters": [
    {{
      "id": "chapter01",
      "title": "<modelin uygun gördüğü başlık>",
      "summary": "<modelin uygun gördüğü açıklama>",
      "estimated_minutes": <modelin uygun gördüğü dakika sayısı>
    }},
    {{ ... }}
  ]
}}

SADECE JSON üret.
"""
    
    def _generate_content_prompt(self, main_topic: str, chapter_title: str, chapter_summary: str, context: str) -> str:
        """Generate content creation prompt for LLM"""
        return f"""
Sen deneyimli bir HİKAYE ANLATICISI ve TARİH YAZARI'sın. Görevin: "{main_topic}" konusunda "{chapter_title}" dönemini akıcı ve ilgi çekici şekilde anlatmak.

📚 KAYNAK VERİLER:
{context}

🎭 ANLATIM TARZI:
- Hikaye anlatıcısı gibi yaz (akıcı, merak uyandırıcı)
- Genel okuyucu için (her seviyede bilgi)
- Kaynaklardaki bilgileri kendi cümlelerinle anlat
- Ara sıra kaynak atıfı yap: "(İnalcık, Devlet-i Aliyye)" gibi

⚖️ YAZIM KURALLARI:
1) SADECE kaynaklardaki GERÇEK bilgileri kullan ama kendi cümlelerinle anlat
2) Kronolojik sıra ve sebep-sonuç zinciri kur
3) Her paragraf 600-900 kelime; akıcı ve ilgi çekici dil
4) Somut örnekler (tarih, kişi, yer) ver; önemi açıkla
5) Tekrar etme; her paragraf yeni bilgi içersin

YAPILANDIRMA:
- Dönemin Genel Karakteri
- Ana Olaylar ve Gelişmeler (kronolojik)
- Önemli Şahsiyetler ve Rolleri
- Toplumsal/Ekonomik Bağlam
- Dönemin Mirası ve Sonraki Etkiler

## {chapter_title}

[Şimdi bu dönemi kaynaklarından aldığın bilgilerle hikaye anlatıcısı tarzında uzun uzun anlat]

⚡ SADECE markdown içerik üret. Düşüncelerini yazma.
"""

    def _sanitize_llm_output(self, text: str) -> str:
        """Clean model output: remove hidden thinking, duplicate lines/paragraphs, stray YAML blocks."""
        if not text:
            return text

        # Remove any <think>...</think> blocks and raw <think> tags
        import re
        text = re.sub(r"<think>[\s\S]*?</think>", "", text, flags=re.IGNORECASE)
        text = re.sub(r"</?think>", "", text, flags=re.IGNORECASE)

        # Remove leading/trailing backticks sections that might wrap the content
        text = re.sub(r"^```[a-zA-Z]*\n", "", text.strip())
        text = re.sub(r"\n```\s*$", "", text)

        # Split into lines and remove consecutive duplicate lines
        lines = text.split('\n')
        cleaned_lines = []
        prev = None
        for line in lines:
            if line.strip() == prev and line.strip() != "":
                continue
            cleaned_lines.append(line)
            prev = line.strip()

        text = "\n".join(cleaned_lines)

        # Remove repeated paragraphs (exact matches) while preserving order
        paragraphs = [p.strip() for p in text.split('\n\n')]
        seen = set()
        unique_paragraphs = []
        for p in paragraphs:
            key = p
            if key and key not in seen:
                unique_paragraphs.append(p)
                seen.add(key)
        text = "\n\n".join(unique_paragraphs)

        # Ensure content is markdown only: strip stray front matter if model produced it
        if text.lstrip().startswith('---'):
            parts = text.split('---', 2)
            if len(parts) == 3:
                text = parts[-1].strip()

        # Lightweight sentence-level de-duplication of very similar adjacent sentences
        try:
            import re
            raw_sentences = re.split(r'(?<=[.!?])\s+', text)
            filtered: List[str] = []
            for s in raw_sentences:
                s_clean = s.strip()
                if not s_clean:
                    continue
                # Compare with last 3 sentences only
                is_similar = False
                for prev_s in filtered[-3:]:
                    prev_words = set(prev_s.lower().split())
                    cur_words = set(s_clean.lower().split())
                    if not cur_words:
                        continue
                    overlap = len(prev_words & cur_words) / max(1, len(cur_words))
                    if overlap >= 0.7:
                        is_similar = True
                        break
                if not is_similar:
                    filtered.append(s_clean)
            text = ' '.join(filtered)
        except Exception:
            pass

        return text
