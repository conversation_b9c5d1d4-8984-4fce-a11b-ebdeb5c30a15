# 🚀 Gemini API Embeddings Kurulum Kılavuzu

Bu kılavuz, mevcut RAG sisteminizde Google Gemini API embeddings'lerini nasıl kullanacağınızı açıklar.

## 📋 Ön Gere<PERSON>

1. **Google AI Studio hesabı**
2. **Gemini API Key**
3. **Python 3.8+**

## 🔑 API Key Alma

1. [Google AI Studio](https://aistudio.google.com/)'ya gidin
2. API anahtarı oluşturun
3. Anahtarınızı güvenli bir yere kopyalayın

## ⚙️ Kurulum

### 1. Gerekli Paketleri Yükleyin [[memory:3196759]]

```bash
pip install -r requirements.txt
```

### 2. API Key Ayarlayın

**Seçenek A: Environment Variable**
```bash
export GEMINI_API_KEY="your-api-key-here"
```

**Windows için:**
```cmd
set GEMINI_API_KEY=your-api-key-here
```

**Seçenek B: .env Dosyası**
```bash
# .env dosyasına ekleyin
GEMINI_API_KEY=your-api-key-here
```

### 3. Konfigürasyonu Güncelleyin

`config.py` dosyasında:

```python
MODEL_CONFIG = {
    # ...
    'embedding_provider': 'gemini',  # 'ollama' → 'gemini' değiştirin
    'gemini_model': 'gemini-embedding-001',
    'gemini_output_dim': 768,  # 128, 256, 512, 768, 1536, 3072
}
```

## 🎛️ Konfigürasyon Seçenekleri

### Embedding Boyutu Seçimi

| Boyut | Kullanım Senaryosu | Performans |
|-------|-------------------|------------|
| 128   | Küçük uygulamalar | En hızlı |
| 256   | Orta uygulamalar  | Hızlı |
| 512   | Standart kullanım | İyi |
| **768**   | **Önerilen**      | **Optimum** |
| 1536  | Yüksek kalite     | Yavaş |
| 3072  | Maksimum kalite   | En yavaş |

### Provider Karşılaştırması

| Özellik | Ollama | Gemini API |
|---------|--------|------------|
| **Konum** | Lokal | Cloud |
| **Hız** | Sistem bağımlı | İnternet bağımlı |
| **Kalite** | Model bağımlı | Google state-of-the-art |
| **Maliyet** | Ücretsiz | API çağrı başı |
| **Token Limit** | Model bağımlı | 2048 token |

## 🔄 Provider Değiştirme

### Ollama → Gemini

```python
# config.py içinde
MODEL_CONFIG = {
    'embedding_provider': 'gemini',  # Bu satırı değiştirin
    # ... diğer ayarlar
}
```

### Gemini → Ollama

```python
# config.py içinde
MODEL_CONFIG = {
    'embedding_provider': 'ollama',  # Bu satırı değiştirin
    # ... diğer ayarlar
}
```

## 🧪 Test Etme [[memory:3196759]]

### 1. Basit Test

```python
from gemini_embeddings import GeminiEmbeddings

# Test embedding oluştur
embeddings = GeminiEmbeddings(api_key="your-api-key")
result = embeddings.embed_query("Test metni")
print(f"Embedding boyutu: {len(result)}")
```

### 2. RAG Sistem Testi

```bash
python app.py
```

Web interface açıldıktan sonra:
1. PDF dosyası yükleyin
2. Learning path oluşturun
3. Logları kontrol edin

## ❗ Troubleshooting

### Hata: API Key Bulunamadı
```
ValueError: Gemini API key not found
```

**Çözüm:**
1. `GEMINI_API_KEY` environment variable'ını kontrol edin
2. `.env` dosyasını kontrol edin

### Hata: İnternet Bağlantısı
```
Error embedding documents: Connection timeout
```

**Çözüm:**
1. İnternet bağlantınızı kontrol edin
2. Firewall ayarlarını kontrol edin
3. Geçici olarak Ollama'ya geri dönün

### Hata: API Limit Aşımı
```
Error: API quota exceeded
```

**Çözüm:**
1. API kullanım limitinizi kontrol edin
2. Bekleyip tekrar deneyin
3. Geçici olarak Ollama'ya geçin

## 💡 En İyi Uygulamalar

1. **Üretim ortamında** API key'i environment variable olarak saklayın
2. **Development'ta** test için küçük embedding boyutu (128-256) kullanın
3. **Production'da** kalite için büyük boyut (768-1536) kullanın
4. **Backup olarak** Ollama konfigürasyonunu saklayın

## 🔄 Migration Rehberi

### Mevcut Ollama Veritabanından Geçiş

1. **Yeni embedding provider'ı test edin**
2. **Eğer çalışıyorsa, yeni veritabanı oluşturun**
3. **PDF'leri yeniden işleyin**
4. **Test edin ve doğrulayın**

```bash
# Eski veritabanını yedekleyin
cp -r learning_paths_db learning_paths_db_ollama_backup

# Yeni sistem ile PDF'leri yeniden işleyin
# Web interface kullanarak tüm PDF'leri tekrar yükleyin
```

## 🌟 Sonuç

Artık sisteminizde hem Ollama hem de Gemini API embeddings'lerini kullanabilirsiniz!

**Gemini API avantajları:**
- ✅ Daha iyi embedding kalitesi
- ✅ Lokalde model gereksinimleri yok  
- ✅ Google'ın state-of-the-art teknolojisi
- ✅ Esnek boyut seçenekleri

**Sorularınız için:** AI asistanınıza danışabilirsiniz.
