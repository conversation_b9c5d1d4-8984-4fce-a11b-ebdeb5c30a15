#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RAG sistemi verilerini temizleme scripti
Kullanım: python clean_data.py
"""

import os
import shutil
from pathlib import Path

def clean_all_data():
    """Tüm RAG sistemi verilerini temizle"""
    print("🧹 RAG sistemi verileri temizleniyor...")
    
    base_dir = Path(__file__).parent.parent
    
    # Temizlenecek klasörler ve dosyalar
    paths_to_clean = [
        base_dir / "outputs" / "content",
        base_dir / "outputs" / "manifest.json", 
        base_dir / "books",
        base_dir / "learning_paths_db",
        base_dir / "uploads"
    ]
    
    cleaned_items = 0
    
    for path in paths_to_clean:
        try:
            if path.is_file():
                path.unlink()
                print(f"✅ Dosya silindi: {path.name}")
                cleaned_items += 1
            elif path.is_dir():
                # İçeriği temizle ama klasörü koru
                for item in path.iterdir():
                    if item.is_file():
                        item.unlink()
                        print(f"✅ Dosya silindi: {path.name}/{item.name}")
                        cleaned_items += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        print(f"✅ Klasör silindi: {path.name}/{item.name}")
                        cleaned_items += 1
        except Exception as e:
            print(f"⚠️  {path.name} temizlenemedi: {e}")
    
    # Gerekli klasörleri yeniden oluştur
    required_dirs = [
        base_dir / "uploads",
        base_dir / "books", 
        base_dir / "outputs" / "content" / "tr",
        base_dir / "outputs" / "content" / "en",
        base_dir / "learning_paths_db"
    ]
    
    for dir_path in required_dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 Klasör hazırlandı: {dir_path.name}")
    
    print(f"\n🎉 Temizlik tamamlandı!")
    print(f"📊 {cleaned_items} öğe temizlendi")
    print(f"✨ Sistem sıfırdan başlamaya hazır!")

if __name__ == "__main__":
    try:
        clean_all_data()
    except KeyboardInterrupt:
        print("\n❌ Temizlik iptal edildi")
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")
