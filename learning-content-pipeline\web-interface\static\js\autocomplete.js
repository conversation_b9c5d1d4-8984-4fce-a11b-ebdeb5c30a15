// ===== AUTOCOMPLETE FUNCTIONALITY =====

// Global topics array
let allTopics = [];

/**
 * Initialize autocomplete functionality
 */
function initializeAutocomplete() {
    // Load topics first
    loadTopics().then(() => {
        // Setup autocomplete for both inputs
        setupAutocomplete('topic', 'topicDropdown');
        setupAutocomplete('pathTopic', 'pathTopicDropdown');
    });
}

/**
 * Load topics for autocomplete
 */
async function loadTopics() {
    try {
        const response = await apiCall('/api/topics');
        allTopics = response.topics || [];
        console.log('🏷️ Topics loaded:', allTopics.length);
    } catch (error) {
        console.error('Konular yüklenemedi:', error);
        allTopics = [];
    }
}

/**
 * Setup autocomplete for an input
 * @param {string} inputId - Input element ID
 * @param {string} dropdownId - Dropdown element ID
 */
function setupAutocomplete(inputId, dropdownId) {
    const input = document.getElementById(inputId);
    const dropdown = document.getElementById(dropdownId);

    if (!input || !dropdown) {
        console.warn(`Autocomplete elements not found: ${inputId}, ${dropdownId}`);
        return;
    }

    // Show dropdown on focus if there are topics
    input.addEventListener('focus', function() {
        if (allTopics.length > 0) {
            showDropdown(input, dropdown, allTopics);
        }
    });

    // Filter and show dropdown on input
    input.addEventListener('input', function() {
        const query = this.value.toLowerCase().trim();
        
        if (query === '') {
            showDropdown(input, dropdown, allTopics);
        } else {
            const filtered = allTopics.filter(topic => 
                topic.toLowerCase().includes(query)
            );
            showDropdown(input, dropdown, filtered);
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!input.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.add('hidden');
        }
    });
}

/**
 * Show dropdown with topics
 * @param {HTMLElement} input - Input element
 * @param {HTMLElement} dropdown - Dropdown element
 * @param {Array} topics - Topics to show
 */
function showDropdown(input, dropdown, topics) {
    if (topics.length === 0) {
        dropdown.classList.add('hidden');
        return;
    }

    dropdown.innerHTML = '';
    
    topics.slice(0, 8).forEach(topic => { // Show max 8 items
        const item = document.createElement('div');
        item.className = 'px-3 py-2 cursor-pointer hover:bg-gray-100 text-sm text-gray-800 border-b border-gray-100 last:border-b-0';
        item.textContent = topic;
        
        item.addEventListener('click', function() {
            input.value = topic;
            dropdown.classList.add('hidden');
            
            // Trigger input event to update any dependent UI
            input.dispatchEvent(new Event('input', { bubbles: true }));
        });
        
        dropdown.appendChild(item);
    });

    dropdown.classList.remove('hidden');
}

// Make functions global
window.loadTopics = loadTopics;

// Auto-initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAutocomplete);
} else {
    initializeAutocomplete();
}
