# RAG Öğrenme Sistemi Web Arayüzü

Modern, kullanıcı dostu bir web arayüzü ile PDF'lerinizi yükleyin ve AI destekli öğrenme patikaları oluşturun.

## 🌟 Özellikler

- **📤 Kolay PDF Yükleme**: S<PERSON>rükle-bırak ile PDF yükleme
- **🧠 AI Destekli Patika Oluşturma**: Yüklenen içeriklerden otomatik öğrenme patikaları
- **📊 Sistem Durumu**: Gerçek zamanlı sistem istatistikleri
- **📱 Modern UI**: Instagram, Twitter tarzı modern arayüz
- **🔄 Gerçek Zamanlı Güncelleme**: Canlı durum takibi

## 🚀 Hızlı Başlangıç

### 1. Önkoşullar

- Python 3.10+
- Ollama kurulu ve çalışır durumda
- Git (zaten mevcut)

### 2. Ollama Kurulumu

```bash
# Ollama'yı indir ve kur: https://ollama.com/download

# Gerekli modelleri yükle
ollama pull qwen3:8b
ollama pull embeddinggemma:latest

# Ollama servisini başlat (yeni terminal)
ollama serve
```

### 3. Web Arayüzünü Başlat

En kolay yol:

```bash
cd learning-content-pipeline/web-interface
python start_server.py
```

Manuel kurulum:

```bash
cd learning-content-pipeline/web-interface

# Sanal ortam oluştur
python -m venv venv
venv\\Scripts\\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Paketleri yükle
pip install -r requirements.txt

# Sunucuyu başlat
python app.py
```

### 4. Web Arayüzünü Aç

Tarayıcınızda: <http://localhost:5000>

## 📖 Kullanım

### PDF Yükleme

1. **Konu Seç**: Mevcut konulardan birini seçin veya yeni konu ekleyin
2. **Kitap Adı**: Kitabın benzersiz adını girin (örn: `inalcik_1973`)
3. **PDF Yükle**: Dosyayı sürükleyip bırakın veya seçin
4. **Yükle**: PDF otomatik olarak işlenip sisteme eklenir

### Öğrenme Patikası Oluşturma

1. **Konu Gir**: Hangi konuda patika istediğinizi yazın
2. **Dil Seç**: Türkçe veya İngilizce
3. **Oluştur**: AI otomatik olarak bölümler ve içerik üretir

### Sistem İzleme

Ana sayfa üzerinde:

- Toplam metin parçası sayısı
- Yüklenen kitap sayısı
- Oluşturulan patika sayısı
- Ollama bağlantı durumu

## 🏗️ Sistem Mimarisi

```text
web-interface/
├── app.py              # Ana Flask uygulaması
├── start_server.py     # Otomatik başlatıcı script
├── requirements.txt    # Python bağımlılıkları
├── templates/
│   ├── base.html      # Ana şablon
│   └── index.html     # Ana sayfa
└── README.md          # Bu dosya

../
├── uploads/           # Yüklenen dosyalar (geçici)
├── books/            # Organize edilmiş kitaplar
│   ├── konu1/
│   │   └── kitap1/
│   │       └── dosya.pdf
├── outputs/          # Üretilen içerikler
│   └── content/
│       ├── tr/       # Türkçe içerikler
│       └── en/       # İngilizce içerikler
└── learning_paths_db/ # ChromaDB vektör veritabanı
```

## 🔧 API Endpoints

- `GET /` - Ana sayfa
- `GET /api/status` - Sistem durumu
- `POST /api/upload` - PDF yükleme
- `POST /api/create-learning-path` - Patika oluşturma
- `GET /api/learning-paths` - Mevcut patikalar

## 🎨 UI Bileşenleri

### Ana Özellikler

- **Modern Kartlar**: Instagram/Facebook tarzı kart tasarımı
- **Drag & Drop**: Dosya yükleme için sürükle-bırak
- **Toast Bildirimler**: Modern bildirim sistemi (snackbar yerine)
- **Loading Animasyonları**: İşlem sırasında göstergeleri
- **Responsive**: Mobil ve desktop uyumlu

### Renk Paleti

- Primary: İndigo (PDF yükleme)
- Secondary: Mor (AI patikaları)
- Success: Yeşil (başarılı işlemler)
- Warning: Sarı (uyarılar)
- Error: Kırmızı (hatalar)

## 🛠️ Konfigürasyon

### Ortam Değişkenleri

```bash
FLASK_ENV=development
OLLAMA_URL=http://localhost:11434
MAX_FILE_SIZE=100MB
```

### Desteklenen Dosya Formatları

- PDF (öncelikli)
- TXT (yedek)

### Desteklenen Konular

- Osmanlı Tarihi
- Sanayi Devrimi
- Kavimler Göçü
- Peygamberimizin Hayatı
- Özel konular

## 🔍 Sorun Giderme

### Ollama Bağlantı Hatası

```bash
# Terminal'de Ollama'yı başlatın
ollama serve

# Modellerin yüklü olduğunu kontrol edin
ollama list
```

### Paket Yükleme Hataları

```bash
# Pip'i güncelleyin
python -m pip install --upgrade pip

# Requirements'ı tekrar yükleyin
pip install -r requirements.txt --upgrade
```

### Dosya Yükleme Sorunları

- Dosya boyutu 100MB'dan küçük olmalı
- Sadece PDF formatı desteklenir
- Dosya adında özel karakter olmamalı

### Patika Oluşturma Sorunları

- Ollama modellerinin yüklü olduğundan emin olun
- En az bir kitabın yüklü olduğunu kontrol edin
- İnternet bağlantısını kontrol edin (ilk kurulum için)

## 📝 Geliştirme Notları

### Kod Yapısı

- `WebRAGSystem`: RAG işlemleri için ana sınıf
- Flask route'ları RESTful API tasarımı
- Frontend vanilla JavaScript (framework bağımsızlığı)
- TailwindCSS ile responsive tasarım

### Güvenlik

- Dosya türü kontrolü
- Dosya boyutu sınırlaması
- Güvenli dosya adlandırma
- CSRF koruması

### Performans

- Chunked file upload
- Async işlemler için loading göstergeleri
- Otomatik stats güncelleme (30s)
- Responsive image loading

## 🤝 Katkıda Bulunma

1. Bu projeyi fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## 📄 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.

## 🙋‍♂️ Destek

Sorun yaşıyorsanız:

1. Bu README'yi kontrol edin
2. GitHub Issues'da arama yapın
3. Yeni issue açın

---

**Hazırlayan**: AI Asistan  
**Versiyon**: 1.0  
**Son Güncelleme**: 2024
