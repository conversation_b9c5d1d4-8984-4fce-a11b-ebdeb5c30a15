/* ===== CUSTOM STYLES FOR RAG SYSTEM ===== */

/* Card hover effects */
.card-hover {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Drag and drop styles */
.upload-area {
    transition: all 0.3s ease;
    border: 2px dashed #d1d5db;
}

.upload-area.dragover {
    border-color: #6366f1;
    background-color: #f8fafc;
    transform: scale(1.02);
}

.upload-area:hover {
    border-color: #6366f1;
    background-color: #f8fafc;
}

/* File list styles */
.file-item {
    transition: background-color 0.2s ease;
    min-height: 2.5rem; /* Ensure consistent height */
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    margin: 0 8px; /* Add margin to keep borders inside container */
}

.file-item:last-child {
    border-bottom: none; /* Remove border from last item */
}

.file-item:hover {
    background-color: rgba(59, 130, 246, 0.05);
    border-radius: 4px; /* Add slight rounding on hover */
    margin: 0 4px; /* Reduce margin on hover for better effect */
}

.file-name {
    flex: 1;
    min-width: 0; /* Allow shrinking */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 80px); /* Reserve space for file size */
}

.file-size {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    font-weight: 500;
    color: #2563eb;
    background-color: rgba(37, 99, 235, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    white-space: nowrap;
    flex-shrink: 0; /* Prevent shrinking */
    margin-left: 8px;
}

/* Selected files container */
#selectedFilesList {
    max-height: 300px; /* Limit height to prevent page overflow */
    overflow-y: auto; /* Add scroll if needed */
    padding: 4px 0; /* Add padding to contain margins */
}

/* Custom scrollbar for file list - minimal design */
#selectedFilesList::-webkit-scrollbar {
    width: 4px;
}

#selectedFilesList::-webkit-scrollbar-track {
    background: transparent;
}

#selectedFilesList::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;
}

#selectedFilesList::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
}

/* Loading animations */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Smooth transitions */
.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

/* Loading overlay styles */
.loading-overlay {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Custom scrollbar for dropdowns */
.max-h-48::-webkit-scrollbar {
    width: 6px;
}

.max-h-48::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus styles */
.focus\\:ring-indigo-500:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-opacity: 0.5;
    --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));
}

.focus\\:ring-purple-500:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-opacity: 0.5;
    --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity));
}

/* Button hover effects */
button:hover {
    transition: all 0.2s ease;
}

/* Form input focus effects */
input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Chapter number circles */
.chapter-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

/* Status indicators */
.status-success {
    color: #10b981;
}

.status-error {
    color: #ef4444;
}

.status-warning {
    color: #f59e0b;
}

.status-info {
    color: #3b82f6;
}

/* Responsive design helpers */
@media (max-width: 640px) {
    .card-hover:hover {
        transform: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .upload-area.dragover {
        transform: scale(1.01);
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}

/* Print styles */
@media print {
    .loading-overlay,
    button,
    .upload-area {
        display: none !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card-hover:hover {
        box-shadow: 0 0 0 2px #000;
    }
    
    button:focus {
        outline: 2px solid #000;
        outline-offset: 2px;
    }
}

/* Toast notification styles */
.toast-notification {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.toast-notification:hover {
    transform: translateX(0) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Toast container responsive positioning */
@media (max-width: 640px) {
    #toast-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
    
    .toast-notification {
        max-width: none;
    }
}

/* Toast entrance animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Progress bar for toast duration */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.3);
    animation: progress 5s linear forwards;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* Fade-in animation for modals */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.2s ease-out;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .animate-spin,
    .animate-pulse,
    .transition-all,
    .transition-colors,
    .card-hover,
    .toast-notification {
        animation: none;
        transition: none;
    }
    
    .toast-progress {
        display: none;
    }
}
