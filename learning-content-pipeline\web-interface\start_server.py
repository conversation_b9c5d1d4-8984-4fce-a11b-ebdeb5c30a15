#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
RAG Öğrenme Sistemi Web Arayüzü Başlatıcı
Bu script web arayüzünü başlatır ve gerekli kontrolleri yapar
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_ollama():
    """Ollama servisinin çalışıp çalışmadığını kontrol et"""
    try:
        response = requests.get('http://localhost:11434', timeout=5)
        return True
    except:
        return False

def check_python_packages():
    """Gerekli Python paketlerinin yüklü olup olmadığını kontrol et"""
    required_packages = [
        'flask',
        'langchain',
        'chromadb',
        'pypdf'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_packages():
    """Eksik paketleri yükle"""
    print("📦 Gerekli paketler yükleniyor...")
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Paketler başarıyla yüklendi!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Paket yükleme hatası: {e}")
        return False

def create_directories():
    """Gerekli klasörleri oluştur"""
    base_dir = Path(__file__).parent.parent
    directories = [
        base_dir / "uploads",
        base_dir / "books",
        base_dir / "outputs" / "content" / "tr",
        base_dir / "outputs" / "content" / "en",
        base_dir / "learning_paths_db"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"📁 Klasör oluşturuldu: {directory}")

def check_ollama_models():
    """Gerekli Ollama modellerinin yüklü olup olmadığını kontrol et"""
    try:
        # Ollama model listesini al
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        models_output = result.stdout
        
        required_models = ['qwen', 'embed']  # qwen3:8b ve embeddinggemma için arama
        missing_models = []
        
        for model in required_models:
            if model not in models_output.lower():
                missing_models.append(model)
        
        return missing_models
    except:
        return ['qwen', 'embed']  # Ollama komutları çalışmıyorsa tüm modeller eksik varsay

def main():
    print("🚀 RAG Öğrenme Sistemi Web Arayüzü Başlatıcı")
    print("=" * 50)
    
    # 1. Ollama kontrolü
    print("🔍 Ollama servisi kontrol ediliyor...")
    if not check_ollama():
        print("❌ Ollama servisi çalışmıyor!")
        print("\n📝 Ollama'yı başlatmak için:")
        print("   1. Yeni bir terminal açın")
        print("   2. 'ollama serve' komutunu çalıştırın")
        print("   3. Bu scripti tekrar çalıştırın")
        
        choice = input("\nOllama servisini başlattınız mı? (e/h): ").lower()
        if choice != 'e':
            print("Çıkış yapılıyor...")
            return
        
        # Tekrar kontrol et
        if not check_ollama():
            print("❌ Ollama servisi hala çalışmıyor. Lütfen kontrol edin.")
            return
    
    print("✅ Ollama servisi çalışıyor!")
    
    # 2. Modeller kontrolü
    print("🔍 Ollama modelleri kontrol ediliyor...")
    missing_models = check_ollama_models()
    
    if missing_models:
        print("❌ Eksik modeller tespit edildi!")
        print("\n📥 Gerekli modelleri yüklemek için aşağıdaki komutları çalıştırın:")
        print("   ollama pull qwen3:8b")
        print("   ollama pull embeddinggemma:latest")
        
        choice = input("\nModelleri yüklediniz mi? (e/h): ").lower()
        if choice != 'e':
            print("Çıkış yapılıyor...")
            return
    else:
        print("✅ Ollama modelleri hazır!")
    
    # 3. Python paketleri kontrolü
    print("🔍 Python paketleri kontrol ediliyor...")
    missing_packages = check_python_packages()
    
    if missing_packages:
        print(f"❌ Eksik paketler: {', '.join(missing_packages)}")
        
        choice = input("Eksik paketleri yüklemek istiyor musunuz? (e/h): ").lower()
        if choice == 'e':
            if not install_packages():
                print("❌ Paket yükleme başarısız. Lütfen manuel olarak yükleyin.")
                return
        else:
            print("Çıkış yapılıyor...")
            return
    else:
        print("✅ Tüm Python paketleri yüklü!")
    
    # 4. Klasör yapısını oluştur
    print("🔍 Klasör yapısı kontrol ediliyor...")
    create_directories()
    print("✅ Klasör yapısı hazır!")
    
    # 5. Flask uygulamasını başlat
    print("\n🌐 Web arayüzü başlatılıyor...")
    print("=" * 50)
    print("📍 Adres: http://localhost:5000")
    print("🛑 Durdurmak için: Ctrl+C")
    print("=" * 50)
    
    # app.py'yi import et ve çalıştır
    try:
        from app import app, init_rag_system
        
        # RAG sistemini başlat
        print("🧠 RAG sistemi başlatılıyor...")
        if init_rag_system():
            print("✅ RAG sistemi hazır!")
            
            # Web sunucusunu başlat
            app.run(debug=False, host='0.0.0.0', port=5000)
        else:
            print("❌ RAG sistemi başlatılamadı!")
            
    except ImportError as e:
        print(f"❌ Import hatası: {e}")
        print("Lütfen app.py dosyasının mevcut olduğundan emin olun.")
    except KeyboardInterrupt:
        print("\n\n👋 Web arayüzü kapatılıyor...")
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")

if __name__ == "__main__":
    main()
