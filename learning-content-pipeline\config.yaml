lang: tr
# <PERSON><PERSON> baz<PERSON> - her konu i<PERSON>in far<PERSON> pathId
pathId: auto-detect  # Model konuya göre otomatik belirler
chunk:
  size_tokens: 900
  overlap_tokens: 150
embed_model: BAAI/bge-m3
llm:
  provider: ollama
  model: qwen3:8b
  temperature: 0.3
  max_tokens: 1800
outputs:
  cache_dir: vectorstore
  out_dir: outputs
books:
  source_dir: books
  processed_dir: training_data
  citation_format: academic
  # Desteklenen konular ve kitapları
  topics:
    sanayi_devrimi:
      name: "Sanayi Devrimi"
      books: ["allen_2009", "mokyr_1990", "pomeranz_2000", "landes_1998"]
    osmanli_tarihi:
      name: "Osmanlı Tarihi"
      books: ["inalcik_1973", "faroqhi_2004", "finkel_2005", "quataert_2005"]
    kavimler_gocu:
      name: "Kavimler Göçü"
      books: ["heather_2005", "goffart_1980", "wolfram_1997", "halsall_2007"]
    peygamber_hayati:
      name: "Peygamberimizin Hayatı"
      books: ["lings_1983", "armstrong_1992", "ramadan_2007", "aslan_2005"]
training:
  fine_tune_model: false  # İlk aşamada RAG kullan, sonra fine-tuning
  custom_model_name: multi-topic-expert
  epochs: 3
  batch_size: 4
  learning_rate: 2e-5
