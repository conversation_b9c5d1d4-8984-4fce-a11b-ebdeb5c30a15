# RAG Learning System - Web Interface Context

This document provides comprehensive context for the RAG (Retrieval-Augmented Generation) Learning System web interface, a Flask-based application that creates AI-powered learning paths from PDF documents.

## Project Overview

The RAG Learning System is a web-based platform that allows users to:
1. Upload PDF documents on various topics
2. Create AI-generated learning paths from the uploaded content
3. View and manage created learning paths in multiple languages

The system uses Ollama for language modeling and embeddings, with ChromaDB as the vector database for document storage and retrieval.

## Technology Stack

- **Backend**: Python 3.10+, Flask
- **Frontend**: HTML, TailwindCSS, Vanilla JavaScript
- **AI Services**: Ollama (qwen3:8b model, embeddinggemma model)
- **Database**: ChromaDB (vector database)
- **Document Processing**: PyPDF, LangChain
- **Deployment**: Standalone Python application

## Project Structure

```
web-interface/
├── app.py                 # Main Flask application entry point
├── start_server.py        # Application startup script with dependency checks
├── config.py              # Configuration settings and app initialization
├── rag_system.py          # Core RAG system implementation
├── routes.py              # Flask route definitions
├── utils.py               # Utility functions
├── clean_data.py          # Data cleanup script
├── requirements.txt       # Python dependencies
├── run.bat                # Windows batch script to start the application
├── .env                   # Environment variables
├── README.md              # Project documentation
├── GEMINI_SETUP_GUIDE.md  # Guide for using Gemini API embeddings (alternative to Ollama)
├── templates/             # HTML templates
│   ├── base.html          # Base template with common layout
│   ├── index.html         # Main dashboard page
│   └── learning_path.html # Learning path view page
├── static/                # Static assets
│   ├── css/               # Stylesheets
│   └── js/                # JavaScript files
│       ├── utils.js       # Utility functions (toasts, loading, API calls)
│       ├── upload.js      # File upload functionality
│       ├── books.js       # Book management
│       ├── paths.js       # Learning path management
│       ├── autocomplete.js # Autocomplete features
│       └── main.js        # Main application logic
├── uploads/               # Temporary upload directory
├── books/                 # Organized book files by topic
├── outputs/               # Generated content and learning paths
│   └── content/
│       ├── tr/            # Turkish content
│       └── en/            # English content
└── learning_paths_db/     # ChromaDB vector database
```

## Key Features

1. **PDF Upload System**:
   - Drag-and-drop interface
   - Support for multiple file uploads
   - Topic-based organization
   - Automatic document processing and chunking

2. **AI Learning Path Generation**:
   - Multi-language support (8 languages)
   - Context-aware content generation
   - RAG-based document retrieval
   - Structured chapter organization

3. **Content Management**:
   - Book library with pagination
   - Learning path listing and deletion
   - System statistics dashboard
   - Data cleanup utilities

4. **Modern UI Components**:
   - Responsive design with TailwindCSS
   - Interactive toast notifications
   - Loading states and animations
   - Card-based layout inspired by social media platforms

## Core Components

### WebRAGSystem (rag_system.py)
The main RAG system class that handles:
- Document processing and vector storage
- AI-powered learning path creation
- Content generation using LLM
- Document retrieval with similarity search

### Flask Routes (routes.py)
API endpoints include:
- `/api/status` - System statistics
- `/api/upload` - PDF upload
- `/api/create-learning-path` - Learning path generation
- `/api/delete-learning-path` - Path deletion
- `/api/topics` - Topic listing
- `/api/books` - Book listing with pagination

### Configuration (config.py)
Key configurations:
- Model settings (LLM and embedding models)
- Directory paths and file handling
- Rate limiting for API endpoints
- Text processing parameters

## Setup and Running

### Prerequisites
1. Python 3.10+
2. Ollama installed and running
3. Required Ollama models:
   - `qwen3:8b` (LLM)
   - `embeddinggemma:latest` (Embeddings)

### Quick Start
1. Install Ollama from https://ollama.com/download
2. Pull required models:
   ```bash
   ollama pull qwen3:8b
   ollama pull embeddinggemma:latest
   ```
3. Start Ollama service:
   ```bash
   ollama serve
   ```
4. Run the web interface:
   - Windows: Double-click `run.bat` or run `python start_server.py`
   - Other: `python start_server.py`
5. Access the application at http://localhost:5000

### Manual Installation
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt

# Start the application
python app.py
```

## Development Guidelines

### Code Structure
- Modular design with separate files for configuration, routing, and core logic
- RESTful API design for frontend-backend communication
- Comprehensive error handling and logging
- Security measures for file uploads and user input

### Frontend Development
- Vanilla JavaScript with modular architecture
- TailwindCSS for responsive styling
- Component-based design with reusable utilities
- Modern UI patterns (toasts, loading states, confirmations)

### Security Considerations
- File type validation for uploads
- Path traversal prevention
- Input sanitization and validation
- Rate limiting for API endpoints
- Secure temporary file handling

## API Endpoints

### GET Routes
- `GET /` - Main dashboard
- `GET /api/status` - System status and statistics
- `GET /api/topics` - Available topics for autocomplete
- `GET /api/books` - Book listing with pagination
- `GET /api/learning-paths` - Existing learning paths
- `GET /path/<path_id>` - View specific learning path

### POST Routes
- `POST /api/upload` - Upload PDF documents
- `POST /api/create-learning-path` - Generate learning path
- `POST /api/delete-learning-path` - Delete specific path
- `POST /api/delete-all-learning-paths` - Delete all paths
- `POST /api/delete-book` - Delete specific book
- `POST /api/delete-all-books` - Delete all books
- `POST /api/cleanup-orphaned-files` - Cleanup temporary files

## Maintenance Utilities

1. **Data Cleanup**: `python clean_data.py` - Remove all processed data and reset system
2. **System Status**: Real-time monitoring through dashboard
3. **Log Monitoring**: Python logging throughout the application

## Troubleshooting

### Common Issues
1. **Ollama Connection**: Ensure Ollama service is running (`ollama serve`)
2. **Model Loading**: Verify required models are installed (`ollama list`)
3. **File Upload Errors**: Check file size limits and format restrictions
4. **Rate Limiting**: Respect API rate limits (3 path creations per hour, 10 uploads per hour)

### Performance Considerations
- Large PDF processing may take time
- Learning path generation uses multiple LLM calls
- Vector database grows with content
- Memory usage increases with document count

This context should provide a comprehensive understanding of the RAG Learning System web interface for future development and maintenance tasks.