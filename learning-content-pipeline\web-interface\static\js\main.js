// ===== MAIN APPLICATION INITIALIZATION =====

/**
 * Initialize the entire application with error boundaries
 */
async function initializeApp() {
    console.log('🚀 Initializing RAG System Web Interface...');
    
    try {
        // Set up global error handlers first
        if (typeof setupGlobalErrorHandlers === 'function') {
            setupGlobalErrorHandlers();
        }
        
        // Initialize all modules with error boundaries
        await safeExecute(async () => {
            if (typeof initializeUpload === 'function') {
                initializeUpload();
            }
        }, 'upload initialization');
        
        await safeExecute(async () => {
            if (typeof initializePaths === 'function') {
                initializePaths();
            }
        }, 'paths initialization');
        
        await safeExecute(async () => {
            if (typeof initializeAutocomplete === 'function') {
                initializeAutocomplete();
            }
        }, 'autocomplete initialization');
        
        // Load initial data with small delay to ensure DOM is fully ready
        setTimeout(async () => {
            console.log('🚀 DOM hazır, veri y<PERSON><PERSON><PERSON> başlatılıyor...');
            
            // Load data with error boundaries and retry logic
            const dataLoadingPromises = [
                safeExecute(async () => {
                    if (typeof loadBooks === 'function') {
                        await withRetry(() => loadBooks(), {
                            maxAttempts: 2,
                            onRetry: (error, attempt) => {
                                console.log(`📚 Retrying book load (attempt ${attempt}):`, error.message);
                            }
                        });
                    }
                }, 'books loading'),
                
                safeExecute(async () => {
                    if (typeof loadLearningPaths === 'function') {
                        await withRetry(() => loadLearningPaths(), {
                            maxAttempts: 2,
                            onRetry: (error, attempt) => {
                                console.log(`🛤️ Retrying paths load (attempt ${attempt}):`, error.message);
                            }
                        });
                    }
                }, 'learning paths loading'),
                
                safeExecute(async () => {
                    if (typeof refreshStats === 'function') {
                        await withRetry(() => refreshStats(), {
                            maxAttempts: 2,
                            onRetry: (error, attempt) => {
                                console.log(`📊 Retrying stats load (attempt ${attempt}):`, error.message);
                            }
                        });
                    }
                }, 'stats refreshing')
            ];
            
            // Wait for all data loading with timeout
            try {
                await Promise.allSettled(dataLoadingPromises);
                console.log('✅ Initial data loading completed');
            } catch (error) {
                console.error('❌ Some data loading failed:', error);
                showToast('Bazı veriler yüklenemedi. Sayfa yenilenerek tekrar denenecek.', 'warning', 5000);
            }
        }, 100);
        
        console.log('✅ Application initialization completed');
        
    } catch (error) {
        console.error('❌ Critical application initialization error:', error);
        showToast('Uygulama başlatılırken kritik bir hata oluştu. Sayfayı yenileyin.', 'error', 10000);
    }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Global error handler for unhandled promises
window.addEventListener('unhandledrejection', function(event) {
    console.error('🔥 Unhandled promise rejection:', event.reason);
    // Optionally show user-friendly error
    if (typeof showToast === 'function') {
        showToast('Beklenmeyen bir hata oluştu', 'error');
    }
});

// Global error handler
window.addEventListener('error', function(event) {
    console.error('🔥 Global error:', event.error);
    if (typeof showToast === 'function') {
        showToast('Sayfa hatası oluştu', 'error');
    }
});

console.log('📊 RAG System Web Interface modules loaded');
