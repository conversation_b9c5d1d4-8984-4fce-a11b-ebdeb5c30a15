"""
RAG Learning System - Main Application Entry Point
A modular web-based Retrieval-Augmented Generation system for creating learning paths
"""
import logging

from config import create_app, ensure_directories, rate_limiter
from rag_system import WebRAGSystem
from routes import register_routes
from utils import cleanup_orphaned_files

# Logging configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_rag_system():
    """Initialize RAG system"""
    try:
        rag_system = WebRAGSystem()
        
        # Update the global variable in config
        import config
        config.rag_system = rag_system
        
        logger.info("✅ RAG system successfully initialized")
        return True
    except Exception as e:
        logger.error(f"❌ RAG system could not be initialized: {e}")
        return False

def main():
    """Main application entry point with enhanced startup procedures"""
    try:
        # Create Flask app
        app = create_app()
        
        # Ensure necessary directories exist
        ensure_directories()
        
        # Perform startup cleanup
        logger.info("🧹 Performing startup cleanup...")
        try:
            from config import UPLOAD_FOLDER, BOOKS_FOLDER, OUTPUTS_FOLDER
            cleanup_orphaned_files(UPLOAD_FOLDER, max_age_hours=1)
            cleanup_orphaned_files(BOOKS_FOLDER, max_age_hours=24)
            cleanup_orphaned_files(OUTPUTS_FOLDER, max_age_hours=24)
            rate_limiter.cleanup_old_entries()
            logger.info("✅ Startup cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Startup cleanup failed: {e}")
        
        # Register all routes
        register_routes(app)
        
        # Initialize RAG system
        if init_rag_system():
            logger.info("🚀 Web interface starting...")
            logger.info("📍 Available at: http://localhost:5000")
            logger.info("🛑 Press Ctrl+C to stop")
            app.run(debug=True, host='0.0.0.0', port=5000)
        else:
            print("❌ RAG system could not be initialized. Make sure Ollama service is running.")
            
    except KeyboardInterrupt:
        logger.info("👋 Application shutting down...")
    except Exception as e:
        logger.error(f"💥 Critical application error: {e}")
        print("❌ Critical error occurred. Check logs for details.")

if __name__ == '__main__':
    main()
