"""
Utility functions for RAG Learning System
"""
import json
import logging
import re
import html
import os
import tempfile
from contextlib import contextmanager
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

from config import BOOKS_FOLDER, OUTPUTS_FOLDER

logger = logging.getLogger(__name__)

def allowed_file(filename: str) -> bool:
    """Check if file extension is allowed"""
    from config import ALLOWED_EXTENSIONS
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename(name: str) -> str:
    """Create Windows-safe file/folder name"""
    # Remove invalid characters for Windows: < > : " | ? * / \
    name = re.sub(r'[<>:"|?*\\\/]', '', name)
    # Replace multiple spaces with single space
    name = re.sub(r'\s+', ' ', name)
    # Strip leading/trailing spaces
    name = name.strip()
    # Truncate if too long (Windows has 260 character limit)
    if len(name) > 100:
        name = name[:100]
    return name

def validate_and_sanitize_input(input_str: str, field_name: str, max_length: int = 255, min_length: int = 1) -> Tuple[bool, str, str]:
    """
    Validate and sanitize user input comprehensively.
    
    Args:
        input_str: The input string to validate
        field_name: Name of the field (for error messages)
        max_length: Maximum allowed length
        min_length: Minimum required length
        
    Returns:
        Tuple of (is_valid, sanitized_input, error_message)
    """
    if not input_str:
        return False, "", f"{field_name} is required"
    
    # Strip whitespace
    sanitized = input_str.strip()
    
    # Check length constraints
    if len(sanitized) < min_length:
        return False, sanitized, f"{field_name} must be at least {min_length} characters long"
    
    if len(sanitized) > max_length:
        return False, sanitized, f"{field_name} cannot exceed {max_length} characters"
    
    # HTML escape for security
    sanitized = html.escape(sanitized)
    
    # Check for suspicious patterns
    suspicious_patterns = [
        r'<script[\s\S]*?</script>',  # Script tags
        r'javascript:',               # JavaScript protocol
        r'on\w+\s*=',                # Event handlers
        r'data:.*base64',            # Data URLs with base64
        r'[<>]',                     # Remaining HTML tags
    ]
    
    for pattern in suspicious_patterns:
        if re.search(pattern, sanitized, re.IGNORECASE):
            return False, sanitized, f"{field_name} contains invalid characters or patterns"
    
    # Additional validation for topic/book names
    if field_name.lower() in ['topic', 'book name', 'book_name']:
        # Allow letters, numbers, spaces, and common punctuation
        if not re.match(r'^[\w\s\-\.\,\(\)]+$', sanitized):
            return False, sanitized, f"{field_name} contains invalid characters. Only letters, numbers, spaces, and common punctuation are allowed"
    
    return True, sanitized, ""

def sanitize_topic_name(topic: str) -> Tuple[bool, str, str]:
    """
    Sanitize and validate topic names specifically.
    
    Returns:
        Tuple of (is_valid, sanitized_topic, error_message)
    """
    return validate_and_sanitize_input(topic, "Topic", max_length=100, min_length=2)

def sanitize_book_name(book_name: str) -> Tuple[bool, str, str]:
    """
    Sanitize and validate book names specifically.
    
    Returns:
        Tuple of (is_valid, sanitized_book_name, error_message)
    """
    return validate_and_sanitize_input(book_name, "Book name", max_length=200, min_length=2)

def is_safe_path_component(component: str) -> bool:
    """
    Check if a string is safe to use as a path component.
    
    Args:
        component: The path component to check
        
    Returns:
        True if safe, False otherwise
    """
    if not component or component.strip() != component:
        return False
    
    # Check for path traversal attempts
    dangerous_patterns = ['..', '/', '\\', '~', '$', '`', ';', '|', '&', '<', '>']
    
    for pattern in dangerous_patterns:
        if pattern in component:
            return False
    
    # Check for reserved Windows names
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    
    if component.upper() in reserved_names:
        return False
    
    return True

def validate_file_upload(file_data: Dict) -> Tuple[bool, str]:
    """
    Validate file upload data comprehensively.
    
    Args:
        file_data: Dictionary containing file information
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not file_data.get('filename'):
        return False, "Filename is required"
    
    filename = file_data['filename']
    
    # Check file extension
    if not allowed_file(filename):
        return False, "Only PDF files are allowed"
    
    # Check file size (100MB limit)
    max_size = 100 * 1024 * 1024
    if file_data.get('size', 0) > max_size:
        return False, f"File size cannot exceed {max_size // (1024 * 1024)}MB"
    
    # Check filename for suspicious content
    if not is_safe_path_component(filename):
        return False, "Filename contains invalid characters"
    
    return True, ""

@contextmanager
def safe_file_operation(file_path: Path, cleanup_on_failure: bool = True):
    """
    Context manager for safe file operations with automatic cleanup on failure.
    
    Args:
        file_path: Path to the file being operated on
        cleanup_on_failure: Whether to delete the file if an exception occurs
        
    Usage:
        with safe_file_operation(Path('temp_file.txt')) as safe_path:
            # File operations here
            safe_path.write_text('content')
            # If an exception occurs, file will be automatically cleaned up
    """
    try:
        yield file_path
    except Exception as e:
        if cleanup_on_failure and file_path.exists():
            try:
                file_path.unlink()
                logger.info(f"Cleaned up file after error: {file_path}")
            except Exception as cleanup_error:
                logger.warning(f"Failed to cleanup file {file_path}: {cleanup_error}")
        raise e

class TempFileManager:
    """
    Manages temporary files and ensures cleanup even if processing fails.
    """
    
    def __init__(self):
        self.temp_files: List[Path] = []
        self.temp_dirs: List[Path] = []
    
    def create_temp_file(self, suffix: str = '', prefix: str = 'rag_', directory: Optional[Path] = None) -> Path:
        """
        Create a temporary file that will be automatically cleaned up.
        
        Args:
            suffix: File suffix (e.g., '.pdf', '.txt')
            prefix: File prefix
            directory: Directory to create temp file in (default: system temp)
            
        Returns:
            Path to the temporary file
        """
        if directory:
            directory.mkdir(parents=True, exist_ok=True)
        
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=directory)
        os.close(fd)  # Close file descriptor, but keep the file
        
        temp_file = Path(temp_path)
        self.temp_files.append(temp_file)
        return temp_file
    
    def create_temp_dir(self, prefix: str = 'rag_temp_', parent_dir: Optional[Path] = None) -> Path:
        """
        Create a temporary directory that will be automatically cleaned up.
        
        Args:
            prefix: Directory prefix
            parent_dir: Parent directory (default: system temp)
            
        Returns:
            Path to the temporary directory
        """
        temp_dir = Path(tempfile.mkdtemp(prefix=prefix, dir=parent_dir))
        self.temp_dirs.append(temp_dir)
        return temp_dir
    
    def cleanup_all(self):
        """Clean up all temporary files and directories."""
        # Clean up temporary files
        for temp_file in self.temp_files[:]:
            try:
                if temp_file.exists():
                    temp_file.unlink()
                    logger.debug(f"Cleaned up temp file: {temp_file}")
                self.temp_files.remove(temp_file)
            except Exception as e:
                logger.warning(f"Failed to cleanup temp file {temp_file}: {e}")
        
        # Clean up temporary directories
        for temp_dir in self.temp_dirs[:]:
            try:
                if temp_dir.exists():
                    import shutil
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Cleaned up temp directory: {temp_dir}")
                self.temp_dirs.remove(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to cleanup temp directory {temp_dir}: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - always cleanup."""
        self.cleanup_all()

def cleanup_orphaned_files(base_directory: Path, max_age_hours: int = 24):
    """
    Clean up orphaned temporary files older than specified age.
    
    Args:
        base_directory: Directory to scan for orphaned files
        max_age_hours: Maximum age in hours before files are considered orphaned
    """
    if not base_directory.exists():
        return
    
    import time
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    cleaned_count = 0
    
    try:
        # Look for files that match temporary file patterns
        patterns = ['**/rag_temp_*', '**/temp_*', '**/*_temp.*', '**/tmp_*']
        
        for pattern in patterns:
            for file_path in base_directory.glob(pattern):
                try:
                    # Check if file is old enough
                    file_age = current_time - file_path.stat().st_mtime
                    
                    if file_age > max_age_seconds:
                        if file_path.is_file():
                            file_path.unlink()
                            cleaned_count += 1
                            logger.info(f"Cleaned up orphaned file: {file_path}")
                        elif file_path.is_dir():
                            import shutil
                            shutil.rmtree(file_path)
                            cleaned_count += 1
                            logger.info(f"Cleaned up orphaned directory: {file_path}")
                            
                except Exception as e:
                    logger.warning(f"Failed to process potential orphaned file {file_path}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} orphaned files/directories")
            
    except Exception as e:
        logger.error(f"Error during orphaned file cleanup: {e}")

def get_books(page: int = 1, per_page: int = 50, topic_filter: Optional[str] = None) -> Dict[str, Any]:
    """
    Get book list directly from filesystem with pagination support.
    
    Args:
        page: Page number (1-based)
        per_page: Items per page
        topic_filter: Optional topic filter
        
    Returns:
        Dictionary with books data and pagination info
    """
    books = []
    total_books = 0
    total_topics = 0
    
    try:
        if not BOOKS_FOLDER.exists():
            return {
                "books": [],
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_items": 0,
                    "total_pages": 0
                },
                "stats": {
                    "total_topics": 0,
                    "total_books": 0
                }
            }
        
        # First pass: collect all topics and count
        all_topics = []
        for topic_folder in BOOKS_FOLDER.iterdir():
            if not topic_folder.is_dir():
                continue
                
            # Apply topic filter if provided
            if topic_filter and topic_filter.lower() not in topic_folder.name.lower():
                continue
                
            topic_books = []
            for book_folder in topic_folder.iterdir():
                if not book_folder.is_dir():
                    continue
                    
                book_files = list(book_folder.glob("*.pdf")) + list(book_folder.glob("*.txt"))
                if book_files:
                    topic_books.append({
                        "name": book_folder.name,
                        "files": [f.name for f in book_files],
                        "file_count": len(book_files),
                        "total_size_mb": sum(f.stat().st_size for f in book_files) / (1024*1024)
                    })
                    total_books += 1
            
            if topic_books:
                all_topics.append({
                    "topic": topic_folder.name,
                    "book_count": len(topic_books),
                    "books": topic_books
                })
                total_topics += 1
        
        # Sort topics for consistent pagination
        all_topics.sort(key=lambda x: x["topic"])
        
        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_topics = all_topics[start_idx:end_idx]
        
        # Calculate pagination info
        total_pages = (total_topics + per_page - 1) // per_page
        
        logger.info(f"Book list loaded: {total_topics} topics, {total_books} books (page {page}/{total_pages})")
        
        return {
            "books": paginated_topics,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_items": total_topics,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "stats": {
                "total_topics": total_topics,
                "total_books": total_books
            }
        }
        
    except Exception as e:
        logger.error(f"Book list error: {e}")
        return {
            "books": [],
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_items": 0,
                "total_pages": 0
            },
            "stats": {
                "total_topics": 0,
                "total_books": 0
            }
        }

def get_books_simple() -> List[Dict[str, Any]]:
    """Get simple book list for backward compatibility"""
    result = get_books(page=1, per_page=1000)  # Large page size for compatibility
    return result["books"]

def extract_and_parse_json(response_text: str) -> Dict[str, Any]:
    """Extract and parse JSON from LLM response with enhanced error handling for Gemini API"""
    
    # Handle None or empty input
    if not response_text or response_text.strip() == "":
        logger.warning("Empty or None response received from LLM")
        return _get_default_plan()
    
    # Clean the response text
    response_text = response_text.strip()
    
    # Method 1: Try direct JSON parsing first
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        logger.debug("Direct JSON parsing failed, trying other methods")
    
    # Method 2: Look for JSON block with ```json markers
    json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
    if json_match:
        try:
            json_content = json_match.group(1).strip()
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.debug(f"JSON block parsing failed: {e}")
    
    # Method 3: Look for JSON block with ``` markers (without json specifier)
    json_match = re.search(r'```\s*(.*?)\s*```', response_text, re.DOTALL)
    if json_match:
        try:
            json_content = json_match.group(1).strip()
            # Check if it looks like JSON (starts with { and ends with })
            if json_content.startswith('{') and json_content.endswith('}'):
                return json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.debug(f"Code block parsing failed: {e}")
    
    # Method 4: Look for first JSON block starting with { and ending with }
    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
    if json_match:
        try:
            json_content = json_match.group(0)
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.debug(f"Brace block parsing failed: {e}")
    
    # Method 5: Try to clean common Gemini response artifacts
    cleaned_text = response_text
    # Remove common prefixes/suffixes
    cleaned_text = re.sub(r'^(Here\'s|Here is|The JSON|Response:|Answer:)\s*', '', cleaned_text, flags=re.IGNORECASE)
    cleaned_text = re.sub(r'\s*$', '', cleaned_text)
    
    # Try parsing the cleaned text
    try:
        return json.loads(cleaned_text)
    except json.JSONDecodeError:
        logger.debug("Cleaned text parsing failed")
    
    # If nothing works, return default plan
    logger.warning(f"JSON could not be parsed using any method. Response preview: {response_text[:500]}...")
    return _get_default_plan()

def _get_default_plan() -> Dict[str, Any]:
    """Return default learning plan structure"""
    return {
        "total_chapters": 4,
        "reasoning": "Using default plan because model couldn't respond with valid JSON",
        "chapters": [
            {
                "id": "chapter01",
                "title": "Başlangıç ve Ön Koşullar",
                "summary": "Olayların nasıl başladığı, neden başladığı, ortamın hazır olup olmadığı",
                "estimated_minutes": 10
            },
            {
                "id": "chapter02", 
                "title": "Ana Gelişmeler ve Dönüşümler",
                "summary": "Temel süreçler, değişimler ve gelişmelerin analizi",
                "estimated_minutes": 12
            },
            {
                "id": "chapter03",
                "title": "Etkiler ve Tetiklediği Olaylar",
                "summary": "Konunun doğrudan etkileri ve tetiklediği diğer olaylar",
                "estimated_minutes": 12
            },
            {
                "id": "chapter04",
                "title": "Uzun Vadeli Sonuçlar ve Miras",
                "summary": "Kalıcı etkiler, günümüze yansımalar ve tarihsel önemi",
                "estimated_minutes": 10
            }
        ]
    }

def create_dynamic_manifest(path_id: str, path_folder) -> Dict[str, Any]:
    """Create dynamic manifest from files"""
    chapters = []
    chapter_files = sorted(path_folder.glob("chapter*.md"))
    
    for i, chapter_file in enumerate(chapter_files, 1):
        # Extract title from file
        try:
            with open(chapter_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Get title from first line
                lines = content.split('\n')
                title = f"Bölüm {i:02d}"
                for line in lines[:10]:  # Search in first 10 lines
                    if line.strip().startswith('#'):
                        title = line.strip('#').strip()
                        break
                
                chapters.append({
                    "id": chapter_file.stem,
                    "title": title,
                    "summary": f"{path_id.replace('-', ' ').title()} - {title}",
                    "file": str(chapter_file.relative_to(OUTPUTS_FOLDER)),
                    "readingMinutes": 15
                })
        except Exception:
            chapters.append({
                "id": chapter_file.stem,
                "title": f"Bölüm {i:02d}",
                "summary": f"{path_id.replace('-', ' ').title()} bölümü",
                "file": str(chapter_file.relative_to(OUTPUTS_FOLDER)),
                "readingMinutes": 15
            })
    
    return {
        "pathId": path_id,
        "title": {
            "tr": path_id.replace('-', ' ').title()
        },
        "subtitle": {
            "tr": f"{path_id.replace('-', ' ').title()} hakkında öğrenme patikası"
        },
        "estimatedMinutes": len(chapters) * 15,
        "tags": [path_id.replace('-', ' ')],
        "chapters": chapters
    }
