"""
Configuration module for RAG Learning System
"""
import os
import time
from collections import defaultdict, deque
from pathlib import Path
from typing import <PERSON>ple
from flask import Flask
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Base directories
BASE_DIR = Path(__file__).parent.parent
UPLOAD_FOLDER = BASE_DIR / "uploads"
BOOKS_FOLDER = BASE_DIR / "books"
OUTPUTS_FOLDER = BASE_DIR / "outputs"
DB_FOLDER = BASE_DIR / "learning_paths_db"

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'txt'}

# Model configurations
MODEL_CONFIG = {
    'llm_model': 'gemini-2.5-pro',  # Gemini 2.5 Pro model
    'embedding_model': 'gemini-embedding-001',  # Vertex AI Gemini embedding model
    'ollama_base_url': 'http://localhost:11434',
    'temperature': 0.3,
    'top_p': 0.9,
    'num_ctx': 4096,
    # Provider selection: 'gemini' for Vertex AI, 'ollama' for local
    'embedding_provider': 'gemini',  # Vertex AI Gemini embedding
    'llm_provider': 'gemini',  # Vertex AI Gemini LLM
    # Vertex AI API configuration
    'gemini_api_key': os.getenv('GEMINI_API_KEY'),
    'vertex_project_id': os.getenv('VERTEX_PROJECT_ID', 'your-project-id'),
    'vertex_location': os.getenv('VERTEX_LOCATION', 'us-central1'),
    'google_application_credentials': os.getenv('GOOGLE_APPLICATION_CREDENTIALS'),
}

# Text processing configurations
TEXT_PROCESSING_CONFIG = {
    'chunk_size': 1500,
    'chunk_overlap': 300,
    'separators': ["\n\n", "\n", ". ", " ", ""]
}


# RAG selection behavior (configurable)
RAG_SELECTION_CONFIG = {
    # If True, only include chunks whose metadata.topic matches the requested topic
    # If False, include all semantically similar chunks regardless of topic metadata
    'filter_by_topic': True,
    # Maximum number of document chunks to include in a chapter context.
    # 0 or None means unlimited (be careful: very large contexts can degrade LLM output/performance)
    'max_sources': 10,
    # Retrieval fanout before filtering/diversification
    'initial_k': 20,
    # Use Max Marginal Relevance to diversify results
    'use_mmr': True,
    # Ensure at least one chunk per uploaded book for the topic (if available)
    'ensure_one_per_source': True,
    # Additional knobs
    'mmr_lambda': 0.6,
    'relevance_threshold': 0.0,
    'use_multi_query': True
}


# Language configurations
LANGUAGE_CONFIG = {
    'supported_languages': ['tr', 'en', 'de', 'it', 'ru', 'fr', 'es', 'pt'],
    'language_names': {
        'tr': 'Türkçe',
        'en': 'İngilizce', 
        'de': 'Almanca',
        'it': 'İtalyanca',
        'ru': 'Rusça',
        'fr': 'Fransızca',
        'es': 'İspanyolca',
        'pt': 'Portekizce'
    },
    'default_language': 'tr'
}

def create_app() -> Flask:
    """Create and configure Flask application"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'rag-system-secret-key-2024'
    app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
    
    return app

def ensure_directories():
    """Create necessary directories if they don't exist"""
    for folder in [UPLOAD_FOLDER, BOOKS_FOLDER, OUTPUTS_FOLDER]:
        folder.mkdir(exist_ok=True)


# Rate limiting configuration
RATE_LIMIT_CONFIG = {
    'path_creation': {
        'max_requests': 3,      # Maximum requests
        'time_window': 3600,    # Time window in seconds (1 hour)
        'cooldown_period': 300  # Cooldown period in seconds (5 minutes)
    },
    'file_upload': {
        'max_requests': 10,     # Maximum uploads
        'time_window': 3600,    # Time window in seconds (1 hour)
        'cooldown_period': 60   # Cooldown period in seconds (1 minute)
    }
}

class RateLimiter:
    """
    Simple rate limiter based on IP addresses using sliding window approach.
    """
    
    def __init__(self):
        # Store request timestamps per IP per action type
        self.requests = defaultdict(lambda: defaultdict(deque))
        # Store blocked IPs with cooldown end time
        self.blocked_ips = defaultdict(dict)
    
    def is_allowed(self, ip_address: str, action_type: str) -> Tuple[bool, str]:
        """
        Check if request is allowed for given IP and action type.
        
        Args:
            ip_address: Client IP address
            action_type: Type of action ('path_creation', 'file_upload')
            
        Returns:
            Tuple of (is_allowed, reason_if_blocked)
        """
        current_time = time.time()
        
        # Check if IP is in cooldown period
        if action_type in self.blocked_ips[ip_address]:
            cooldown_end = self.blocked_ips[ip_address][action_type]
            if current_time < cooldown_end:
                remaining = int(cooldown_end - current_time)
                return False, f"Rate limit exceeded. Try again in {remaining} seconds."
            else:
                # Remove expired cooldown
                del self.blocked_ips[ip_address][action_type]
        
        # Get configuration for action type
        config = RATE_LIMIT_CONFIG.get(action_type, RATE_LIMIT_CONFIG['path_creation'])
        max_requests = config['max_requests']
        time_window = config['time_window']
        cooldown_period = config['cooldown_period']
        
        # Clean old requests outside time window
        requests_queue = self.requests[ip_address][action_type]
        while requests_queue and requests_queue[0] < current_time - time_window:
            requests_queue.popleft()
        
        # Check if limit exceeded
        if len(requests_queue) >= max_requests:
            # Add to cooldown
            self.blocked_ips[ip_address][action_type] = current_time + cooldown_period
            return False, f"Rate limit exceeded ({max_requests} requests per hour). Cooldown period: {cooldown_period // 60} minutes."
        
        # Allow request and record timestamp
        requests_queue.append(current_time)
        return True, ""
    
    def get_remaining_requests(self, ip_address: str, action_type: str) -> int:
        """Get number of remaining requests for IP and action type."""
        current_time = time.time()
        config = RATE_LIMIT_CONFIG.get(action_type, RATE_LIMIT_CONFIG['path_creation'])
        time_window = config['time_window']
        max_requests = config['max_requests']
        
        # Clean old requests
        requests_queue = self.requests[ip_address][action_type]
        while requests_queue and requests_queue[0] < current_time - time_window:
            requests_queue.popleft()
        
        return max(0, max_requests - len(requests_queue))
    
    def cleanup_old_entries(self):
        """Clean up old entries to prevent memory bloat."""
        current_time = time.time()
        
        # Clean expired blocked IPs
        for ip in list(self.blocked_ips.keys()):
            for action in list(self.blocked_ips[ip].keys()):
                if current_time >= self.blocked_ips[ip][action]:
                    del self.blocked_ips[ip][action]
            if not self.blocked_ips[ip]:
                del self.blocked_ips[ip]
        
        # Clean old request entries
        max_time_window = max(config['time_window'] for config in RATE_LIMIT_CONFIG.values())
        for ip in list(self.requests.keys()):
            for action in list(self.requests[ip].keys()):
                queue = self.requests[ip][action]
                while queue and queue[0] < current_time - max_time_window:
                    queue.popleft()
                if not queue:
                    del self.requests[ip][action]
            if not self.requests[ip]:
                del self.requests[ip]

# Global rate limiter instance
rate_limiter = RateLimiter()

# Global RAG system instance (will be initialized later)
rag_system = None
