<!DOCTYPE html>
<html lang="tr" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RAG Öğrenme Sistemi{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/styles.css') }}" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f7fafc;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .success-animation {
            animation: successPulse 0.6s ease-out;
        }
        @keyframes successPulse {
            0% { transform: scale(0.95); opacity: 0.7; }
            50% { transform: scale(1.02); opacity: 0.9; }
            100% { transform: scale(1); opacity: 1; }
        }
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="h-full bg-gray-50">
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="gradient-bg shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold text-white">RAG Öğrenme Sistemi</h1>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="refreshStats()" class="text-white hover:text-gray-200 transition-colors duration-200">
                            <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                        </button>
                        <div id="systemStatus" class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                            <span class="text-white text-sm">Durum kontrol ediliyor...</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Modern toast container will be created dynamically by utils.js -->

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="flex flex-col items-center">
                    <div class="loading-spinner rounded-full h-12 w-12 mb-4"></div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">İşlem Devam Ediyor</h3>
                    <p id="loadingText" class="text-sm text-gray-500 text-center">Lütfen bekleyin...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Global variables
        let systemStats = null;

        // Legacy showToast compatibility - redirects to modern toast system
        function showToast(message, type = 'info', duration = 5000) {
            // This will call the enhanced toast function from utils.js
            if (window.showToast && window.showToast !== showToast) {
                return window.showToast(message, type, duration);
            }
            console.log('Toast:', message, type);
        }

        // Show/hide loading modal
        function showLoading(text = 'Lütfen bekleyin...') {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingModal').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingModal').classList.add('hidden');
        }

        // Refresh system stats
        async function refreshStats() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (response.ok) {
                    systemStats = data;
                    updateStatusIndicator(data);
                    updateStatsDisplay(data);
                } else {
                    throw new Error(data.error || 'Durum alınamadı');
                }
            } catch (error) {
                console.error('Durum yenileme hatası:', error);
                updateStatusIndicator({ ollama_status: false });
                showToast('Sistem durumu alınamadı: ' + error.message, 'error');
            }
        }

        // Update status indicator
        function updateStatusIndicator(data) {
            const statusDiv = document.getElementById('systemStatus');
            const isOnline = data.ollama_status;
            
            statusDiv.innerHTML = `
                <div class="w-2 h-2 ${isOnline ? 'bg-green-400' : 'bg-red-400'} rounded-full"></div>
                <span class="text-white text-sm">${isOnline ? 'Sistem Aktif' : 'Ollama Bağlantısı Yok'}</span>
            `;
        }

        // Update stats display
        function updateStatsDisplay(data) {
            // Update stats cards if they exist
            if (document.getElementById('totalChunks')) {
                document.getElementById('totalChunks').textContent = data.total_chunks || 0;
            }
            if (document.getElementById('totalBooks')) {
                const totalBooks = data.books ? data.books.reduce((sum, topic) => sum + topic.books.length, 0) : 0;
                document.getElementById('totalBooks').textContent = totalBooks;
            }
            if (document.getElementById('totalPaths')) {
                document.getElementById('totalPaths').textContent = data.learning_paths ? data.learning_paths.length : 0;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            // Refresh stats every 30 seconds
            setInterval(refreshStats, 30000);
        });

        // API helper functions
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, options);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP error! status: ${response.status}`);
                }
                
                return data;
            } catch (error) {
                console.error('API çağrısı hatası:', error);
                throw error;
            }
        }

        // File upload helper
        function handleFileUpload(file, topic, bookName) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('topic', topic);
            formData.append('book_name', bookName);
            
            return fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
