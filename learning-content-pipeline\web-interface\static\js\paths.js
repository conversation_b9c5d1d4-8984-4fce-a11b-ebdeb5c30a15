// ===== LEARNING PATHS FUNCTIONALITY =====

/**
 * Initialize learning paths functionality
 */
function initializePaths() {
    const createPathForm = document.getElementById('createPathForm');
    if (createPathForm) {
        createPathForm.addEventListener('submit', handlePathCreation);
    }
    
    // Language mode toggle functionality
    initializeLanguageToggle();
}

/**
 * Initialize language selection toggle
 */
function initializeLanguageToggle() {
    const languageModeRadios = document.querySelectorAll('input[name="languageMode"]');
    const singleSelect = document.getElementById('singleLanguageSelect');
    const multipleSelect = document.getElementById('multipleLanguageSelect');
    const allWarning = document.getElementById('allLanguageWarning');
    
    languageModeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Hide all sections first
            singleSelect.classList.remove('hidden');
            multipleSelect.classList.add('hidden');
            allWarning.classList.add('hidden');
            
            // Show appropriate section
            switch(this.value) {
                case 'single':
                    singleSelect.classList.remove('hidden');
                    break;
                case 'multiple':
                    singleSelect.classList.add('hidden');
                    multipleSelect.classList.remove('hidden');
                    break;
                case 'all':
                    singleSelect.classList.add('hidden');
                    allWarning.classList.remove('hidden');
                    break;
            }
        });
    });
}

/**
 * Handle learning path creation form submission
 */
async function handlePathCreation(e) {
    e.preventDefault();

    const topic = document.getElementById('pathTopic')?.value.trim();
    
    if (!topic) {
        showToast('Lütfen bir konu girin!', 'warning', 3000);
        return;
    }

    // Get selected language mode and languages
    const languageMode = document.querySelector('input[name="languageMode"]:checked')?.value || 'single';
    let selectedLanguages = [];
    
    switch(languageMode) {
        case 'single':
            selectedLanguages = [document.getElementById('pathLanguage')?.value || 'tr'];
            break;
        case 'multiple':
            const checkedBoxes = document.querySelectorAll('.language-checkbox:checked');
            selectedLanguages = Array.from(checkedBoxes).map(cb => cb.value);
            if (selectedLanguages.length === 0) {
                showToast('Lütfen en az bir dil seçin!', 'warning', 3000);
                return;
            }
            break;
        case 'all':
            selectedLanguages = ['tr', 'en', 'de', 'it', 'ru', 'fr', 'es', 'pt'];
            break;
    }
    
    console.log('📚 Seçilen diller:', selectedLanguages);
    
    if (selectedLanguages.length > 1) {
        const confirmed = await showConfirm(
            `${selectedLanguages.length} farklı dilde patika oluşturulacak:\n${getLanguageNames(selectedLanguages).join(', ')}\n\nBu işlem uzun sürebilir. Devam etmek istiyor musunuz?`,
            'Çoklu Dil Patikası'
        );
        
        if (!confirmed) {
            return;
        }
    }

    try {
        const loadingMessage = selectedLanguages.length > 1 
            ? `${selectedLanguages.length} dilde öğrenme patikası oluşturuluyor...\nBu işlem uzun sürebilir, lütfen bekleyin.`
            : 'Öğrenme patikası oluşturuluyor...\nBu işlem birkaç dakika sürebilir.';
            
        showLoading(loadingMessage);

        const response = await fetch('/api/create-learning-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                topic, 
                languages: selectedLanguages 
            })
        });

        const result = await response.json();

        if (response.ok) {
            const languageCount = selectedLanguages.length;
            const chapterCount = result.learning_path.chapters.length;
            const message = languageCount > 1 
                ? `${languageCount} dilde öğrenme patikası oluşturuldu! Her dil için ${chapterCount} bölüm hazırlandı.`
                : `Öğrenme patikası oluşturuldu! ${chapterCount} bölüm hazırlandı.`;
                
            showToast(message, 'success', 7000);
            document.getElementById('createPathForm')?.reset();
            
            // Reset language mode to single
            const singleModeRadio = document.querySelector('input[name="languageMode"][value="single"]');
            if (singleModeRadio) {
                singleModeRadio.checked = true;
                singleModeRadio.dispatchEvent(new Event('change'));
            }
            
            await refreshStats();
            await loadLearningPaths();
            if (typeof loadTopics === 'function') {
                await loadTopics();
            }
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showToast('Patika oluşturma hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

/**
 * Load learning paths directly
 */
async function loadLearningPaths() {
    const container = document.getElementById('pathsContainer');
    
    // DOM element kontrolü
    if (!container) {
        console.error('❌ pathsContainer elementi bulunamadı!');
        return;
    }
    
    try {
        const response = await apiCall('/api/learning-paths');

        if (!response.paths || response.paths.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i data-lucide="map" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                    <p class="text-lg font-medium text-gray-600 mb-2">Henüz öğrenme patikası oluşturulmamış</p>
                    <p class="text-sm">AI ile yeni patika oluşturun</p>
                </div>
            `;
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
            return;
        }

        const pathsHTML = `
            <div class="flex items-center justify-between mb-3">
                <div class="text-sm text-gray-600">Toplam patika: ${response.paths.length}</div>
                <button onclick="deleteAllLearningPaths()" class="text-sm text-red-600 hover:text-red-800 flex items-center px-3 py-1 border border-red-200 rounded hover:bg-red-50">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                    Hepsini Sil
                </button>
            </div>
            ` + response.paths.map(path => `
            <div class="border border-gray-200 rounded-lg p-4 card-hover cursor-pointer transition-all duration-200 hover:shadow-lg" 
                 onclick="window.open('/path/${path.pathId}', '_blank')">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-lg font-medium text-gray-900 flex items-center">
                        <i data-lucide="route" class="w-5 h-5 mr-2 text-purple-600"></i>
                        ${path.title}
                        <i data-lucide="external-link" class="w-4 h-4 ml-2 text-gray-400"></i>
                    </h4>
                    <button onclick="event.stopPropagation(); deleteLearningPath('${path.pathId}', '${path.title}')" 
                            class="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors duration-200 z-10 relative"
                            title="Patikayı sil">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    ${path.chapters.map((chapter, index) => `
                        <div class="bg-gray-50 rounded p-3 flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-sm font-medium mr-3">
                                ${index + 1}
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">${chapter.title}</p>
                                <p class="text-xs text-gray-500">Bölüm</p>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');

        container.innerHTML = pathsHTML;
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
        }
    } catch (error) {
        console.error('Patika yükleme hatası:', error);
        if (container) {
            container.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4"></i>
                    <p>Patikalar yüklenirken hata oluştu</p>
                    <button onclick="loadLearningPaths()" class="mt-2 text-sm text-blue-600 hover:text-blue-800">Tekrar Dene</button>
                </div>
            `;
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
        }
    }
}

/**
 * Delete learning path
 * @param {string} pathId - Path ID to delete
 * @param {string} pathTitle - Path title for confirmation
 */
async function deleteLearningPath(pathId, pathTitle) {
    console.log('🗑️ Patika silme başlatıldı:', pathId, pathTitle);
    
    const confirmed = await showConfirm(
        `"${pathTitle}" patikasını silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz ve tüm bölüm içerikleri silinecektir.`,
        'Patikayı Sil'
    );
    
    if (!confirmed) {
        console.log('❌ Kullanıcı silme işlemini iptal etti');
        return;
    }

    try {
        showLoading('Patika siliniyor...');
        console.log('📡 API çağrısı gönderiliyor...');

        const response = await fetch('/api/delete-learning-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path_id: pathId })
        });

        const result = await response.json();
        console.log('📡 API yanıtı:', response.status, result);

        if (response.ok) {
            showToast(result.message, 'success', 5000);
            console.log('🔄 UI güncellemesi başlatılıyor...');
            
            await refreshStats(); // Stats güncelle
            await loadLearningPaths(); // Patika listesini yenile
            if (typeof loadTopics === 'function') {
                await loadTopics(); // Topic listesini yenile
            }
            
            console.log('✅ UI güncellendi');
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showToast('Silme hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

/**
 * Delete all learning paths (both TR and EN folders)
 */
async function deleteAllLearningPaths() {
    const confirmed = await showConfirm(
        'Tüm öğrenme patikaları (TR ve varsa EN) silinecek. Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
        'Hepsini Sil'
    );
    if (!confirmed) return;

    try {
        showLoading('Tüm patikalar siliniyor...');
        const response = await fetch('/api/delete-all-learning-paths', { method: 'POST' });
        const result = await response.json();
        if (response.ok) {
            showToast(result.message || 'Tüm patikalar silindi', 'success', 6000);
            await refreshStats();
            await loadLearningPaths();
            if (typeof loadTopics === 'function') {
                await loadTopics();
            }
        } else {
            throw new Error(result.error || 'Silme hatası');
        }
    } catch (error) {
        showToast('Silme hatası: ' + error.message, 'error', 8000);
    } finally {
        hideLoading();
    }
}

/**
 * Get language names for display
 * @param {Array} languageCodes - Array of language codes
 * @returns {Array} Array of language names
 */
function getLanguageNames(languageCodes) {
    const languageMap = {
        'tr': 'Türkçe',
        'en': 'İngilizce',
        'de': 'Almanca',
        'it': 'İtalyanca',
        'ru': 'Rusça',
        'fr': 'Fransızca',
        'es': 'İspanyolca',
        'pt': 'Portekizce'
    };
    
    return languageCodes.map(code => languageMap[code] || code);
}

// Make functions global
window.loadLearningPaths = loadLearningPaths;
window.deleteLearningPath = deleteLearningPath;
window.deleteAllLearningPaths = deleteAllLearningPaths;

// Auto-initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePaths);
} else {
    initializePaths();
}
